<?xml version='1.0' encoding='utf-8' standalone='yes' ?>
<map>
    <string name="vicelauncher_ep32_dock_app_data_1.2">[&#10;  {&#10;    &quot;appName&quot;: &quot;home&quot;,&#10;    &quot;className&quot;: &quot;com.android.vicelauncher.MainActivity&quot;,&#10;    &quot;cloudConfigFlag&quot;: true,&#10;    &quot;icon&quot;: &quot;dock_home&quot;,&#10;    &quot;isUsed&quot;: true,&#10;    &quot;packageName&quot;: &quot;com.android.vicelauncher&quot;,&#10;    &quot;redPointFlag&quot;: false&#10;  },&#10;  {&#10;    &quot;appName&quot;: &quot;btHeadset&quot;,&#10;    &quot;className&quot;: &quot;&quot;,&#10;    &quot;cloudConfigFlag&quot;: true,&#10;    &quot;icon&quot;: &quot;dock_bt_headset_enable&quot;,&#10;    &quot;isUsed&quot;: true,&#10;    &quot;packageName&quot;: &quot;&quot;,&#10;    &quot;redPointFlag&quot;: false&#10;  },&#10;  {&#10;    &quot;appName&quot;: &quot;appList&quot;,&#10;    &quot;className&quot;: &quot;&quot;,&#10;    &quot;cloudConfigFlag&quot;: true,&#10;    &quot;icon&quot;: &quot;dock_applist&quot;,&#10;    &quot;isUsed&quot;: true,&#10;    &quot;packageName&quot;: &quot;&quot;,&#10;    &quot;redPointFlag&quot;: false&#10;  },&#10;  {&#10;    &quot;appName&quot;: &quot;车上娱乐&quot;,&#10;    &quot;className&quot;: &quot;&quot;,&#10;    &quot;cloudConfigFlag&quot;: true,&#10;    &quot;icon&quot;: &quot;dock_asusement&quot;,&#10;    &quot;isUsed&quot;: true,&#10;    &quot;packageName&quot;: &quot;com.cybertron.browser&quot;,&#10;    &quot;redPointFlag&quot;: false&#10;  },&#10;  {&#10;    &quot;appName&quot;: &quot;乐播投屏&quot;,&#10;    &quot;className&quot;: &quot;&quot;,&#10;    &quot;cloudConfigFlag&quot;: true,&#10;    &quot;icon&quot;: &quot;dock_lebo&quot;,&#10;    &quot;isUsed&quot;: true,&#10;    &quot;packageName&quot;: &quot;com.hpplay.happyplay.aw&quot;,&#10;    &quot;redPointFlag&quot;: false&#10;  },&#10;  {&#10;    &quot;appName&quot;: &quot;QQ音乐&quot;,&#10;    &quot;className&quot;: &quot;&quot;,&#10;    &quot;cloudConfigFlag&quot;: true,&#10;    &quot;icon&quot;: &quot;dock_qqmusic&quot;,&#10;    &quot;isUsed&quot;: true,&#10;    &quot;packageName&quot;: &quot;com.tencent.qqmusiccar&quot;,&#10;    &quot;redPointFlag&quot;: false&#10;  },&#10;  {&#10;    &quot;appName&quot;: &quot;喜马拉雅&quot;,&#10;    &quot;className&quot;: &quot;&quot;,&#10;    &quot;cloudConfigFlag&quot;: true,&#10;    &quot;icon&quot;: &quot;dock_ximalaya&quot;,&#10;    &quot;isUsed&quot;: true,&#10;    &quot;packageName&quot;: &quot;com.ximalaya.ting.android.car&quot;,&#10;    &quot;redPointFlag&quot;: false&#10;  },&#10;  {&#10;    &quot;appName&quot;: &quot;哪吒爱玩&quot;,&#10;    &quot;className&quot;: &quot;&quot;,&#10;    &quot;cloudConfigFlag&quot;: true,&#10;    &quot;icon&quot;: &quot;dock_game&quot;,&#10;    &quot;isUsed&quot;: true,&#10;    &quot;packageName&quot;: &quot;com.desaysv.ep40_gamecenter&quot;,&#10;    &quot;redPointFlag&quot;: false&#10;  }&#10;]</string>
</map>
