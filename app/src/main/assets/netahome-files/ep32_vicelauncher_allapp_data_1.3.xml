<?xml version='1.0' encoding='utf-8' standalone='yes' ?>
    <map>
        <string name="ep32_vicelauncher_allapp_data_1.3">[
  {
    "appName": "雷石KTV",
    "className": "",
    "cloudConfigFlag": true,
    "icon": "dock_ksong",
    "isUsed": true,
    "packageName": "com.thunder.carplay",
    "redPointFlag": false
  },
  {
    "appName": "乐播投屏",
    "className": "",
    "cloudConfigFlag": true,
    "icon": "dock_lebo",
    "isUsed": true,
    "packageName": "com.hpplay.happyplay.aw",
    "redPointFlag": false
  },
  {
    "appName": "爱奇艺",
    "className": "",
    "cloudConfigFlag": true,
    "icon": "dock_aiqiyi",
    "isUsed": true,
    "packageName": "com.arcvideo.car.iqy.video",
    "redPointFlag": false
  },
  {
    "appName": "火山车娱",
    "className": "",
    "cloudConfigFlag": true,
    "icon": "dock_huoshang",
    "isUsed": true,
    "packageName": "com.bytedance.byteautoservice",
    "redPointFlag": false
  },
  {
    "appName": "EasyTouch",
    "className": "com.shere.easytouch.application.SplashActivity",
    "cloudConfigFlag": true,
    "icon": "",
    "isUsed": true,
    "packageName": "com.shere.easytouci",
    "redPointFlag": false
  },
  {
    "appName": "MusicFree",
    "className": "fun.upup.musicfree.MainActivity",
    "cloudConfigFlag": true,
    "icon": "",
    "isUsed": true,
    "packageName": "fun.upup.musicfree",
    "redPointFlag": false
  },
  {
    "appName": "网易云音乐",
    "className": "com.netease.cloudmusic.home.MainActivity",
    "cloudConfigFlag": true,
    "icon": "",
    "isUsed": true,
    "packageName": "com.netease.cloudmusic.iot",
    "redPointFlag": false
  },
  {
    "appName": "洛雪音乐",
    "className": "cn.toside.music.mobile.MainActivity",
    "cloudConfigFlag": true,
    "icon": "",
    "isUsed": true,
    "packageName": "cn.toside.music.mobile",
    "redPointFlag": false
  },
  {
    "appName": "Via",
    "className": "mark.via.Shell",
    "cloudConfigFlag": true,
    "icon": "",
    "isUsed": true,
    "packageName": "mark.via",
    "redPointFlag": false
  },
  {
    "appName": "Simple Live",
    "className": "com.xycz.simple_live/.MainActivity",
    "cloudConfigFlag": true,
    "icon": "",
    "isUsed": true,
    "packageName": "com.xycz.simple_live",
    "redPointFlag": false
  }
]</string>
    </map>
    