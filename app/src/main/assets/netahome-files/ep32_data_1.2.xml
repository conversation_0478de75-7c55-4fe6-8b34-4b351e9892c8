<?xml version='1.0' encoding='utf-8' standalone='yes' ?>
              <map>
                
    <string name="dock.data">[
  {
    "appName": "home",
    "className": "com.hozonauto.navlbs.home.mvvm.ui.activity.MainHomeActivity",
    "cloudConfigFlag": true,
    "icon": "dock_home",
    "isNewAdd": false,
    "isUsed": true,
    "packageName": "com.hozonauto.launcher",
    "redPointFlag": false
  },
  {
    "appName": "system",
    "className": "com.hozon.settings.MainActivity",
    "cloudConfigFlag": true,
    "icon": "dock_system",
    "isNewAdd": false,
    "isUsed": true,
    "packageName": "com.hozon.settings",
    "redPointFlag": false
  },
  {
    "appName": "appList",
    "className": "",
    "cloudConfigFlag": true,
    "icon": "dock_applist",
    "isNewAdd": false,
    "isUsed": true,
    "packageName": "com.hozonauto.netahome",
    "redPointFlag": false
  },
  {
    "appName": "亿联手机互联",
    "className": "net.easyconn.ui.StandMainActivity",
    "cloudConfigFlag": true,
    "icon": "",
    "isNewAdd": false,
    "isUsed": false,
    "packageName": "net.easyconn",
    "redPointFlag": false
  },
  {
    "appName": "空调开关",
    "className": "",
    "cloudConfigFlag": true,
    "icon": "allapps_icon_shortcut_hvac_idle_comfort_night",
    "isNewAdd": false,
    "isUsed": false,
    "packageName": "shortcut_hvac",
    "redPointFlag": false
  },
  {
    "appName": "循环模式",
    "className": "",
    "cloudConfigFlag": true,
    "icon": "allapps_icon_shortcut_outer_loop_idle_comfort_night",
    "isNewAdd": false,
    "isUsed": false,
    "packageName": "shortcut_inner_loop",
    "redPointFlag": false
  },
  {
    "appName": "全景影像",
    "className": "",
    "cloudConfigFlag": true,
    "icon": "allapps_icon_avm_idle",
    "isNewAdd": false,
    "isUsed": false,
    "packageName": "com.hozonauto.panoramic.avm",
    "redPointFlag": false
  }
]</string>
    <string name="applist.data">[
  {
    "appName": "后除霜",
    "className": "",
    "cloudConfigFlag": true,
    "icon": "allapps_icon_shortcut_rear_defrost_idle",
    "isNewAdd": false,
    "isUsed": true,
    "packageName": "shortcut_rear_defrost",
    "redPointFlag": false
  },
  {
    "appName": "主驾加热",
    "className": "",
    "cloudConfigFlag": true,
    "icon": "allapps_icon_shortcut_driver_heat_idle",
    "isNewAdd": false,
    "isUsed": true,
    "packageName": "shortcut_driver_heat",
    "redPointFlag": false
  },
  {
    "appName": "副驾加热",
    "className": "",
    "cloudConfigFlag": true,
    "icon": "allapps_icon_shortcut_secondary_heat_idle",
    "isNewAdd": false,
    "isUsed": true,
    "packageName": "shortcut_secondary_heat",
    "redPointFlag": false
  },
  {
    "appName": "主驾通风",
    "className": "",
    "cloudConfigFlag": true,
    "icon": "allapps_icon_shortcut_driver_ventilate_idle",
    "isNewAdd": false,
    "isUsed": true,
    "packageName": "shortcut_driver_ventilate",
    "redPointFlag": false
  },
  {
    "appName": "副驾通风",
    "className": "",
    "cloudConfigFlag": true,
    "icon": "allapps_icon_shortcut_secondary_ventilate_idle",
    "isNewAdd": false,
    "isUsed": true,
    "packageName": "shortcut_secondary_ventilate",
    "redPointFlag": false
  },
  {
    "appName": "冰箱开关",
    "className": "",
    "cloudConfigFlag": true,
    "icon": "allapps_icon_shortcut_fridge",
    "isNewAdd": false,
    "isUsed": false,
    "packageName": "shortcut_fridge",
    "redPointFlag": false
  },
  {
    "appName": "前除霜",
    "className": "",
    "cloudConfigFlag": true,
    "icon": "allapps_icon_shortcut_front_defrost_idle",
    "isNewAdd": false,
    "isUsed": false,
    "packageName": "shortcut_front_defrost",
    "redPointFlag": false
  },
  {
    "appName": "座椅",
    "className": "",
    "cloudConfigFlag": true,
    "icon": "allapps_icon_shortcut_seat_idle",
    "isNewAdd": false,
    "isUsed": false,
    "packageName": "shortcut_seat",
    "redPointFlag": false
  },
  {
    "appName": "",
    "className": "",
    "cloudConfigFlag": true,
    "icon": "shortcut_placeholder",
    "isNewAdd": false,
    "isUsed": false,
    "packageName": "shortcut_placeholder",
    "redPointFlag": false
  },
  {
    "appName": "车上娱乐",
    "className": "com.cybertron.browser.main.MainActivity",
    "cloudConfigFlag": true,
    "icon": "allapps_icon_cybertron_xlarge_idle",
    "isNewAdd": false,
    "isUsed": true,
    "packageName": "com.cybertron.browser",
    "redPointFlag": false
  },
  {
    "appName": "爱奇艺",
    "className": "com.arcvideo.car.video.SplashActivity",
    "cloudConfigFlag": true,
    "icon": "allapps_icon_iqy_xlarge_idle",
    "isNewAdd": false,
    "isUsed": true,
    "packageName": "com.arcvideo.car.iqy.video",
    "redPointFlag": false
  },
  {
    "appName": "火山车娱",
    "className": "com.bytedance.auto.lite20.uishell.ui.MainActivity",
    "cloudConfigFlag": true,
    "icon": "allapps_icon_mountainfire_xlarge_idle",
    "isNewAdd": false,
    "isUsed": true,
    "packageName": "com.bytedance.byteautoservice",
    "redPointFlag": false
  },
  {
    "appName": "QQ音乐",
    "className": "com.tencent.qqmusiccar.app.activity.AppStarterActivity",
    "cloudConfigFlag": true,
    "icon": "allapps_icon_qqmusic_xlarge_idle",
    "isNewAdd": false,
    "isUsed": true,
    "packageName": "com.tencent.qqmusiccar",
    "redPointFlag": false
  },
  {
    "appName": "喜马拉雅",
    "className": "com.ximalaya.ting.android.car.business.module.splash.WelActivity",
    "cloudConfigFlag": true,
    "icon": "allapps_icon_ximalaya_xlarge_idle",
    "isNewAdd": false,
    "isUsed": true,
    "packageName": "com.ximalaya.ting.android.car",
    "redPointFlag": false
  },
  {
    "appName": "雷石KTV",
    "className": "com.thunder.carplay.home.ui.activity.HomeFragmentActivity",
    "cloudConfigFlag": true,
    "icon": "allapps_icon_karaoke_xxlarge_idle",
    "isNewAdd": false,
    "isUsed": true,
    "packageName": "com.thunder.carplay",
    "redPointFlag": false
  },
  {
    "appName": "本地多媒体",
    "className": "",
    "cloudConfigFlag": true,
    "icon": "allapps_icon_local_multimedia_xlarge_idle",
    "isNewAdd": false,
    "isUsed": true,
    "packageName": "com.hozonauto.localradiomusic",
    "redPointFlag": false
  },
  {
    "appName": "哪吒爱玩",
    "className": "",
    "cloudConfigFlag": true,
    "icon": "allapps_icon_neta_game_xlarge_idle",
    "isNewAdd": false,
    "isUsed": true,
    "packageName": "com.desaysv.ep40_gamecenter_portrait",
    "redPointFlag": false
  },
  {
    "appName": "乐播投屏",
    "className": "",
    "cloudConfigFlag": true,
    "icon": "allapps_icon_happyplay_xlarge_idle",
    "isNewAdd": false,
    "isUsed": true,
    "packageName": "com.hpplay.happyplay.aw",
    "redPointFlag": false
  },
  {
    "appName": "相机",
    "className": "com.hozonauto.camera.CameraActivity",
    "cloudConfigFlag": true,
    "icon": "allapps_icon_camera_xlarge_idle",
    "isNewAdd": false,
    "isUsed": true,
    "packageName": "com.hozonauto.camera",
    "redPointFlag": false
  },
  {
    "appName": "相册",
    "className": "com.hozonauto.gallery.HomePageActivity",
    "cloudConfigFlag": true,
    "icon": "allapps_icon_album_xlarge_idle",
    "isNewAdd": false,
    "isUsed": true,
    "packageName": "com.hozonauto.gallery",
    "redPointFlag": false
  },
  {
    "appName": "通讯",
    "className": "com.hozonauto.phonebook.MainActivity",
    "cloudConfigFlag": true,
    "icon": "allapps_icon_phone_xlarge_idle",
    "isNewAdd": false,
    "isUsed": true,
    "packageName": "com.hozonauto.phonebook",
    "redPointFlag": false
  },
  {
    "appName": "能量流",
    "className": "com.hozonauto.energy.MainActivity",
    "cloudConfigFlag": true,
    "icon": "allapps_icon_energy_xlarge_idle",
    "isNewAdd": false,
    "isUsed": true,
    "packageName": "com.hozonauto.energyflow",
    "redPointFlag": false
  },
  {
    "appName": "哪吒语音",
    "className": "com.hozonauto.integrate.ui.activity.MainActivity",
    "cloudConfigFlag": true,
    "icon": "allapps_icon_voice_xlarge_idle",
    "isNewAdd": false,
    "isUsed": true,
    "packageName": "com.hozonauto.integrate",
    "redPointFlag": false
  },
  {
    "appName": "情景模式",
    "className": "com.hozon.profile.MainActivity",
    "cloudConfigFlag": true,
    "icon": "allapps_icon_profile_xlarge_idle",
    "isNewAdd": false,
    "isUsed": true,
    "packageName": "com.hozon.profile",
    "redPointFlag": false
  },
  {
    "appName": "哪吒小课堂",
    "className": "com.hozonauto.manual.ui.activity.ManualActivity",
    "cloudConfigFlag": true,
    "icon": "allapps_icon_user_manual_xlarge_idle",
    "isNewAdd": false,
    "isUsed": true,
    "packageName": "com.hozonauto.manual",
    "redPointFlag": false
  },
  {
    "appName": "个人中心",
    "className": "com.hozonauto.account.ui.activity.WelcomeActivity",
    "cloudConfigFlag": true,
    "icon": "allapps_icon_personal_center_xlarge_idle",
    "isNewAdd": false,
    "isUsed": true,
    "packageName": "com.hozonauto.account",
    "redPointFlag": false
  },
  {
    "appName": "哪吒魔方",
    "className": "com.hozonauto.sceneengine.MainActivity",
    "cloudConfigFlag": true,
    "icon": "allapps_icon_sceneengine_xlarge_idle",
    "isNewAdd": false,
    "isUsed": true,
    "packageName": "com.hozonauto.sceneengine",
    "redPointFlag": false
  },
  {
    "appName": "哪吒玩具箱",
    "className": "com.hozonauto.toybox.MainActivity",
    "cloudConfigFlag": true,
    "icon": "allapps_icon_toybox_xlarge_idle",
    "isNewAdd": false,
    "isUsed": true,
    "packageName": "com.hozonauto.toybox",
    "redPointFlag": false
  },
  {
    "appName": "流量商城",
    "className": "com.hozonauto.thememanager.ui.activity.MainActivity",
    "cloudConfigFlag": true,
    "icon": "allapps_icon_neta_store_xlarge_idle",
    "isNewAdd": false,
    "isUsed": true,
    "packageName": "com.hozonauto.thememanager",
    "redPointFlag": false
  },
  {
    "appName": "系统升级",
    "className": "com.hozonauto.fota.hmi.view.activity.MainActivity",
    "cloudConfigFlag": true,
    "icon": "allapps_icon_sota_xlarge_idle",
    "isNewAdd": false,
    "isUsed": true,
    "packageName": "com.hozonauto.fota",
    "redPointFlag": false
  },
  {
    "appName": "儿童座椅",
    "className": "com.hozonauto.childseat.MainActivity",
    "cloudConfigFlag": true,
    "icon": "allapps_icon_child_seat_xlarge_idle",
    "isNewAdd": false,
    "isUsed": true,
    "packageName": "com.hozonauto.childseat",
    "redPointFlag": false
  },
  {
    "appName": "洛雪音乐",
    "className": "cn.toside.music.mobile.MainActivity",
    "cloudConfigFlag": true,
    "icon": "",
    "isNewAdd": false,
    "isUsed": true,
    "packageName": "cn.toside.music.mobile",
    "redPointFlag": false
  },
  {
    "appName": "MusicFree",
    "className": "fun.upup.musicfree.MainActivity",
    "cloudConfigFlag": true,
    "icon": "",
    "isNewAdd": false,
    "isUsed": true,
    "packageName": "fun.upup.musicfree",
    "redPointFlag": false
  },
  {
    "appName": "工程模式",
    "className": "com.pateo.factorymode.MainActivity",
    "cloudConfigFlag": true,
    "icon": "",
    "isNewAdd": false,
    "isUsed": true,
    "packageName": "com.pateo.factorymode",
    "redPointFlag": false
  },
  {
    "appName": "Via",
    "className": "mark.via.Shell",
    "cloudConfigFlag": true,
    "icon": "",
    "isNewAdd": false,
    "isUsed": true,
    "packageName": "mark.via",
    "redPointFlag": false
  },
  {
    "appName": "哔哩哔哩",
    "className": "",
    "cloudConfigFlag": true,
    "icon": "",
    "isNewAdd": false,
    "isUsed": false,
    "packageName": "com.bilibili.bilithings",
    "redPointFlag": false
  },
  {
    "appName": "应用商店",
    "className": "",
    "cloudConfigFlag": true,
    "icon": "",
    "isNewAdd": false,
    "isUsed": false,
    "packageName": "com.hozonauto.appstore",
    "redPointFlag": false
  },
  {
    "appName": "Simple Live",
    "className": "com.xycz.simple_live/.MainActivity",
    "cloudConfigFlag": true,
    "icon": "",
    "isNewAdd": false,
    "isUsed": true,
    "packageName": "com.xycz.simple_live",
    "redPointFlag": false
  },
  {
    "appName": "应用管理",
    "className": "com.yunpan.appmanage.ui.HomeActivity",
    "cloudConfigFlag": true,
    "icon": "",
    "isNewAdd": false,
    "isUsed": true,
    "packageName": "com.yunpan.appmanage",
    "redPointFlag": false
  },
  {
    "appName": "My Gesture",
    "className": "me.hisn.mygesture.EA",
    "cloudConfigFlag": true,
    "icon": "",
    "isNewAdd": false,
    "isUsed": true,
    "packageName": "me.hisn.mygesture",
    "redPointFlag": false
  },
  {
    "appName": "自动泊车",
    "className": "com.hozonauto.panoramic.MainActivity",
    "cloudConfigFlag": true,
    "icon": "allapps_icon_panoramic_idle",
    "isNewAdd": false,
    "isUsed": false,
    "packageName": "com.hozonauto.panoramic",
    "redPointFlag": false
  },
  {
    "appName": "腾讯智驾地图",
    "className": "com.tencent.wecarnavi.main.SplashActivity",
    "cloudConfigFlag": true,
    "icon": "",
    "isNewAdd": false,
    "isUsed": false,
    "packageName": "com.tencent.wecarnavi",
    "redPointFlag": false
  }
  
]</string>

              </map>