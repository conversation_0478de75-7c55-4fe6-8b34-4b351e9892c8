<?xml version='1.0' encoding='utf-8' standalone='yes' ?>
<map>
    <string name="ep32_vicelauncher_allapp_data_1.2">[&#10;  {&#10;    &quot;appName&quot;: &quot;爱奇艺&quot;,&#10;    &quot;className&quot;: &quot;&quot;,&#10;    &quot;cloudConfigFlag&quot;: true,&#10;    &quot;icon&quot;: &quot;dock_aiqiyi&quot;,&#10;    &quot;isUsed&quot;: true,&#10;    &quot;packageName&quot;: &quot;com.arcvideo.car.iqy.video&quot;,&#10;    &quot;redPointFlag&quot;: false&#10;  },&#10;  {&#10;    &quot;appName&quot;: &quot;火山车娱&quot;,&#10;    &quot;className&quot;: &quot;&quot;,&#10;    &quot;cloudConfigFlag&quot;: true,&#10;    &quot;icon&quot;: &quot;dock_huoshang&quot;,&#10;    &quot;isUsed&quot;: true,&#10;    &quot;packageName&quot;: &quot;com.bytedance.byteautoservice&quot;,&#10;    &quot;redPointFlag&quot;: false&#10;  },&#10;  {&#10;    &quot;appName&quot;: &quot;雷石KTV&quot;,&#10;    &quot;className&quot;: &quot;&quot;,&#10;    &quot;cloudConfigFlag&quot;: true,&#10;    &quot;icon&quot;: &quot;dock_ksong&quot;,&#10;    &quot;isUsed&quot;: true,&#10;    &quot;packageName&quot;: &quot;com.thunder.carplay&quot;,&#10;    &quot;redPointFlag&quot;: false&#10;  },&#10;  {&#10;    &quot;appName&quot;: &quot;EasyTouch&quot;,&#10;    &quot;className&quot;: &quot;com.shere.easytouch.application.SplashActivity&quot;,&#10;    &quot;cloudConfigFlag&quot;: true,&#10;    &quot;icon&quot;: &quot;icon_star&quot;,&#10;    &quot;isUsed&quot;: true,&#10;    &quot;packageName&quot;: &quot;com.shere.assistivetouch&quot;,&#10;    &quot;redPointFlag&quot;: false&#10;  },&#10;  {&#10;    &quot;appName&quot;: &quot;MusicFree&quot;,&#10;    &quot;className&quot;: &quot;fun.upup.musicfree.MainActivity&quot;,&#10;    &quot;cloudConfigFlag&quot;: true,&#10;    &quot;icon&quot;: &quot;dock_qqmusic&quot;,&#10;    &quot;isUsed&quot;: true,&#10;    &quot;packageName&quot;: &quot;fun.upup.musicfree&quot;,&#10;    &quot;redPointFlag&quot;: false&#10;  },&#10;  {&#10;    &quot;appName&quot;: &quot;工程模式&quot;,&#10;    &quot;className&quot;: &quot;com.pateo.factorymode.MainActivity&quot;,&#10;    &quot;cloudConfigFlag&quot;: true,&#10;    &quot;icon&quot;: &quot;icon_star&quot;,&#10;    &quot;isUsed&quot;: true,&#10;    &quot;packageName&quot;: &quot;acom.pateo.factorymode&quot;,&#10;    &quot;redPointFlag&quot;: false&#10;  },&#10;  {&#10;    &quot;appName&quot;: &quot;网易云音乐&quot;,&#10;    &quot;className&quot;: &quot;com.netease.cloudmusic.home.MainActivity&quot;,&#10;    &quot;cloudConfigFlag&quot;: true,&#10;    &quot;icon&quot;: &quot;dock_qqmusic&quot;,&#10;    &quot;isUsed&quot;: true,&#10;    &quot;packageName&quot;: &quot;com.netease.cloudmusic.iot&quot;,&#10;    &quot;redPointFlag&quot;: false&#10;  },&#10;  {&#10;    &quot;appName&quot;: &quot;洛雪音乐&quot;,&#10;    &quot;className&quot;: &quot;cn.toside.music.mobile.MainActivity&quot;,&#10;    &quot;cloudConfigFlag&quot;: true,&#10;    &quot;icon&quot;: &quot;dock_qqmusic&quot;,&#10;    &quot;isUsed&quot;: true,&#10;    &quot;packageName&quot;: &quot;cn.toside.music.mobile&quot;,&#10;    &quot;redPointFlag&quot;: false&#10;  }&#10;]</string>
</map>
