{"@baidu_map": {"key": "@baidu_map", "name": "@百度地图", "packageName": "com.baidu.naviauto", "icon": "ic_baidu", "launchActivity": "com.baidu.naviauto.SlashActivity", "intent": "com.baidu.naviauto/com.baidu.naviauto.SlashActivity", "activities": ["com.baidu.safemode.debug.SafeModeDebugActivity", "com.baidu.talos.core.scheme.TalosSchemeProxyActivity", "com.baidu.talos.playground.TalosPlayGroundActivity", "com.baidu.sapi2.activity.HorizontalScreenLoginActivity", "com.baidu.baidumaps.cluster.ClusterNaviActivity", "com.baidu.sapi2.activity.ForgetPwdActivity", "com.baidu.naviauto.SlashActivity", "com.baidu.talos.core.devsupport.WebViewActivity", "com.baidu.sapi2.activity.AccountRealNameActivity", "com.baidu.mapautosdk.component.developer.modules.bug.ScreenCropActivity", "com.baidu.sapi2.activity.AccountCenterExternalActivity", "com.baidu.baidumaps.debug.DiagnoseConfigActivity", "com.baidu.sapi2.activity.RegisterActivity", "com.baidu.sapi2.activity.LoadExternalWebViewActivity", "com.baidu.baidumaps.debug.ComDebugActivity", "com.baidu.sapi2.activity.ShareResultProxyActivity", "com.baidu.sapi2.activity.AuthWidgetForCashierActivity", "com.baidu.baidumaps.debug.FdInfoActivity", "com.baidu.sapi2.activity.ShareActivity", "com.baidu.baidumaps.debug.urlreplace.UrlReplaceActivity", "com.baidu.sapi2.activity.CertGuardianActivity", "com.baidu.baidumaps.debug.SplashSDKSettingsActivity", "com.baidu.sapi2.activity.LoginActivity", "com.journeyapps.barcodescanner.CaptureActivity", "com.baidu.sapi2.activity.BindWidgetActivity", "com.baidu.sapi2.activity.BindVerifyActivity", "com.baidu.developer.view.DevelopActivity", "com.baidu.sapi2.activity.ImageClipActivity", "com.baidu.talos.devtools.DevBundleManageActivity", "com.baidu.sapi2.activity.IdCardOcrCameraActivity", "com.baidu.baidumaps.debug.sqlite.SQLiteVisual", "com.baidu.sapi2.activity.OauthActivity", "com.baidu.sapi2.activity.AccountCenterActivity", "com.baidu.baidumaps.debug.CrashInfoActivity", "com.baidu.safemode.SafeModeActivity", "com.baidu.sapi2.activity.QrLoginActivity", "com.baidu.sapi2.activity.ChangeUserNameActivity", "com.baidu.sapi2.activity.AuthWidgetActivity", "com.baidu.baidumaps.debug.SchoolComSettingActivity", "com.baidu.sapi2.activity.ChildVerifyActivity", "com.baidu.baidumaps.MapsActivity", "com.baidu.talos.core.container.TalosActivity", "com.baidu.sapi2.activity.DoubleListActivity", "com.baidu.logback.record.ui.MediaPermissionActivity", "com.baidu.sapi2.share.ShareActivity", "com.baidu.talos.core.devsupport.DevActivity", "com.baidu.sapi2.activity.LoadQrUrlActivity", "com.baidu.sapi2.activity.GrantWebActivity", "com.baidu.sapi2.activity.AuthWidgetOnlyPhoneActivity", "com.baidu.baidumaps.debug.SearchErrorInfoActivity", "com.baidu.baidumaps.debug.TalosDebugActivity", "com.baidu.baidumaps.debug.ProcessCrashInfoActivity", "com.baidu.location.debug.LocDebugActivity", "com.baidu.sapi2.activity.PersonalInfoActivity", "com.baidu.mapframework.webshell.WebShellActivity", "com.baidu.sapi2.activity.RemoteProcessWebviewActivity", "com.baidu.baidumaps.debug.logutil.LogConfigActivity", "com.baidu.sapi2.activity.AccountToolsActivity", "com.baidu.pass.permissions.PermissionsHelperActivity", "com.baidu.sapi2.activity.CurrentProcessWebviewActivity", "com.baidu.baidumaps.debug.BACrashInfoActivity", "com.baidu.talos.playground.SmallCaptureActivity", "com.baidu.mshield.MshieldActivity", "com.baidu.baidumaps.debug.AimeSettingActivity", "com.baidu.baidumaps.debug.XiQueComSettingActivity", "com.baidu.sapi2.activity.SwitchAccountActivity", "com.baidu.talos.devtools.DevRuntimeManagerActivity", "com.baidu.baidumaps.debug.BAProcessCrashInfoActivity", "com.baidu.talos.playground.TalosWebViewActivity", "com.baidu.mapframework.voice.debug2.VoiceDebug2Page", "com.baidu.talos.core.container.TalosDialogActivity", "com.baidu.baidumaps.ugc.favourite.repair.FavDebugPage", "com.baidu.sapi2.activity.NormalizeGuestAccountActivity", "com.baidu.sapi2.activity.YouthStyleLoginActivity", "com.baidu.sofire.MyActivity", "com.baidu.baidumaps.debug.AdComSettingActivity", "com.baidu.naviauto.SimpleWebActivity"]}, "@tencent_map": {"key": "@tencent_map", "name": "@腾讯地图", "packageName": "com.tencent.wecarnavi", "icon": "ic_tencent", "launchActivity": "com.tencent.wecarnavi.main.SplashActivity", "intent": "com.tencent.wecarnavi/com.tencent.wecarnavi.main.SplashActivity", "activities": ["com.tencent.wecar.tts.client.PersonaliseTTSActivity", "com.tencent.wecarspeech.clientsdk.receiver.CmdActivity", "com.didichuxing.doraemonkit.kit.core.TranslucentActivity", "com.didichuxing.doraemonkit.zxing.activity.CaptureActivity", "com.tencent.navibiz.main.MainUIActivity", "com.didichuxing.doraemonkit.kit.core.UniversalActivity", "com.tencent.navibiz.main.policy.PolicyMainActivity", "com.tencent.taes.nettest.NetTestActivity", "com.tencent.wecarnavi.main.SplashActivity", "com.tencent.wecarnavi.main.OnePixelActivity", "com.tencent.taes.cloudres.notification.DialogActivity"]}, "@a_map": {"key": "@a_map", "name": "@高德地图", "packageName": "com.autonavi.amapauto", "icon": "ic_amap", "launchActivity": "com.autonavi.auto.remote.fill.UsbFillActivity", "intent": "com.autonavi.amapauto/com.autonavi.auto.remote.fill.UsbFillActivity", "activities": ["com.alibaba.wireless.security.open.middletier.fc.ui.ExtContainerActivity", "com.autonavi.amapauto.MainMapActivity", "com.alibaba.wireless.security.open.middletier.fc.ui.ContainerActivity", "com.autonavi.auto.remote.fill.UsbFillActivity", "com.autonavi.amapauto.DiagnoseActivity", "com.autonavi.auto.MainMapActivity"]}, "@a_map_mobile": {"key": "@a_map_mobile", "name": "@高德地图手机版", "packageName": "com.autonavi.minimap", "icon": "ic_amap_mobile", "launchActivity": "com.autonavi.map.activity.SplashActivity", "intent": "com.autonavi.minimap/com.autonavi.map.activity.SplashActivity", "activities": ["com.autonavi.map.activity.SplashActivity", "com.autonavi.nebulax.lbs.openlocation.MiniAppOpenLocationActivity", "com.autonavi.profile.QATestInfo.ScreenShotActivity", "com.autonavi.minimap.lite.EmptyActivity", "com.autonavi.map.activity.MagicTextSplashActivity", "com.autonavi.map.activity.QuickSplashActivity", "com.autonavi.minimap.MiPushActivity", "com.autonavi.nebulax.ui.photo.ajx.PhotoActivity", "com.autonavi.map.activity.NewMapActivity", "com.autonavi.map.activity.HicarMapActivity", "com.autonavi.minimap.basemap.errorback.DoorAddressUpload", "com.autonavi.map.activity.CpuArchErrorActivity", "com.autonavi.map.activity.HicarSplashActivity", "com.autonavi.mine.feedback.fragment.ErrorReportListConstants", "com.autonavi.nebulax.lbs.chooselocation.MiniAppChooseLocationActivity", "com.autonavi.gdtaojin.camera.CameraActivity", "com.autonavi.minimap.wxapi.WXPayEntryActivity", "com.autonavi.minimap.drive.search.fragment.SearchCallbackFragment", "com.autonavi.map.activity.NewHicarSplashActivity", "com.autonavi.nebulax.cityselect.oldpage.SelectCityActivity", "com.autonavi.bundle.routecommon.cruise.RouteCruiseActivity", "com.autonavi.map.activity.UcarSplashActivity", "com.autonavi.nebulax.ui.pdfviewer.PdfViewerActivity", "com.autonavi.map.activity.SchemeHandleActivity", "com.autonavi.map.activity.UcarMapActivity", "com.autonavi.minimap.wxapi.WXEntryActivity", "com.autonavi.minimap.route.common.presenter.RouteFragment", "com.autonavi.mine.qrcode.QRCodeScanActivity", "com.autonavi.bundle.account.AccountActivity", "com.autonavi.map.activity.SafeModeActivity", "com.autonavi.map.activity.ErrorActivity", "com.autonavi.minimap.ddshare.DDShareActivity"]}}