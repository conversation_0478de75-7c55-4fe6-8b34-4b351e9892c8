<resources xmlns:tools="http://schemas.android.com/tools">

    <style name="Theme.Main.Light" parent="Theme.Material3.Light.NoActionBar">
        <item name="colorPrimary">@color/md_theme_l_primary</item>
        <item name="colorOnPrimary">@color/md_theme_l_onPrimary</item>
        <item name="colorPrimaryContainer">@color/md_theme_l_primaryContainer</item>
        <item name="colorOnPrimaryContainer">@color/md_theme_l_onPrimaryContainer</item>
        <item name="colorSecondary">@color/md_theme_l_secondary</item>
        <item name="colorOnSecondary">@color/md_theme_l_onSecondary</item>
        <item name="colorSecondaryContainer">@color/md_theme_l_secondaryContainer</item>
        <item name="colorOnSecondaryContainer">@color/md_theme_l_onSecondaryContainer</item>
        <item name="colorTertiary">@color/md_theme_l_tertiary</item>
        <item name="colorOnTertiary">@color/md_theme_l_onTertiary</item>
        <item name="colorTertiaryContainer">@color/md_theme_l_tertiaryContainer</item>
        <item name="colorOnTertiaryContainer">@color/md_theme_l_onTertiaryContainer</item>
        <item name="colorError">@color/md_theme_l_error</item>
        <item name="colorOnError">@color/md_theme_l_onError</item>
        <item name="colorErrorContainer">@color/md_theme_l_errorContainer</item>
        <item name="colorOnErrorContainer">@color/md_theme_l_onErrorContainer</item>
        <item name="android:colorBackground">@color/md_theme_l_background</item>
        <item name="colorOnBackground">@color/md_theme_l_onBackground</item>
        <item name="colorSurface">@color/md_theme_l_surface</item>
        <item name="colorOnSurface">@color/md_theme_l_onSurface</item>
        <item name="colorSurfaceVariant">@color/md_theme_l_surfaceVariant</item>
        <item name="colorOnSurfaceVariant">@color/md_theme_l_onSurfaceVariant</item>
        <item name="colorOutline">@color/md_theme_l_outline</item>
        <item name="colorOutlineVariant">@color/md_theme_l_outlineVariant</item>
        <item name="colorSurfaceInverse">@color/md_theme_l_inverseSurface</item>
        <item name="colorOnSurfaceInverse">@color/md_theme_l_inverseOnSurface</item>
        <item name="colorPrimaryInverse">@color/md_theme_l_inversePrimary</item>
        <item name="colorPrimaryFixed">@color/md_theme_l_primaryFixed</item>
        <item name="colorOnPrimaryFixed">@color/md_theme_l_onPrimaryFixed</item>
        <item name="colorPrimaryFixedDim">@color/md_theme_l_primaryFixedDim</item>
        <item name="colorOnPrimaryFixedVariant">@color/md_theme_l_onPrimaryFixedVariant</item>
        <item name="colorSecondaryFixed">@color/md_theme_l_secondaryFixed</item>
        <item name="colorOnSecondaryFixed">@color/md_theme_l_onSecondaryFixed</item>
        <item name="colorSecondaryFixedDim">@color/md_theme_l_secondaryFixedDim</item>
        <item name="colorOnSecondaryFixedVariant">@color/md_theme_l_onSecondaryFixedVariant</item>
        <item name="colorTertiaryFixed">@color/md_theme_l_tertiaryFixed</item>
        <item name="colorOnTertiaryFixed">@color/md_theme_l_onTertiaryFixed</item>
        <item name="colorTertiaryFixedDim">@color/md_theme_l_tertiaryFixedDim</item>
        <item name="colorOnTertiaryFixedVariant">@color/md_theme_l_onTertiaryFixedVariant</item>
        <item name="colorSurfaceDim">@color/md_theme_l_surfaceDim</item>
        <item name="colorSurfaceBright">@color/md_theme_l_surfaceBright</item>
        <item name="colorSurfaceContainerLowest">@color/md_theme_l_surfaceContainerLowest</item>
        <item name="colorSurfaceContainerLow">@color/md_theme_l_surfaceContainerLow</item>
        <item name="colorSurfaceContainer">@color/md_theme_l_surfaceContainer</item>
        <item name="colorSurfaceContainerHigh">@color/md_theme_l_surfaceContainerHigh</item>
        <item name="colorSurfaceContainerHighest">@color/md_theme_l_surfaceContainerHighest</item>
        <item name="android:statusBarColor">?colorSurfaceContainer</item>
        <item name="android:navigationBarColor">?android:colorBackground</item>
        <item name="android:windowLightStatusBar">true</item>
        <item name="android:windowLightNavigationBar">true</item>

        <item name="toolbarStyle">@style/Theme.Toolbar</item>
        <item name="textInputStyle">@style/Theme.EditText</item>
        <item name="materialAlertDialogTheme">@style/Theme.Alert</item>
        <item name="textAppearanceHeadlineMedium">@style/HeadlineMedium</item>
        <item name="textAppearanceHeadlineSmall">@style/HeadlineSmall</item>
        <item name="buttonStyle">@style/ThemeOverlay.Material3.Button</item>
        <item name="preferenceTheme">@style/ThemeOverlay.App.Preference</item>
    </style>


    <style name="ThemeOverlay.App.Preference" parent="PreferenceThemeOverlay">
        <item name="switchPreferenceCompatStyle">@style/App.Preference.SwitchPreference</item>
        <item name="checkBoxPreferenceStyle">@style/App.Preference.CheckBoxPreference</item>
        <item name="android:background">?android:attr/activatedBackgroundIndicator</item>
    </style>


    <style name="App.Preference.SwitchPreference" parent="Preference.SwitchPreference.Material">
        <item name="android:widgetLayout">@layout/md3_switch_preference</item>
    </style>

    <style name="App.Preference.CheckBoxPreference" parent="Preference.CheckBoxPreference.Material">
        <item name="android:widgetLayout">@layout/md3_checkbox_preference</item>
    </style>

    <style name="HeadlineMedium" parent="TextAppearance.Material3.HeadlineMedium">
        <item name="android:textStyle">bold</item>
        <item name="android:textSize">24sp</item>
        <item name="android:letterSpacing">0</item>
        <item name="lineHeight">32sp</item>
        <item name="android:textAllCaps">false</item>
    </style>

    <style name="HeadlineSmall" parent="TextAppearance.Material3.HeadlineSmall">
        <item name="android:textStyle">normal</item>
        <item name="android:textSize">18sp</item>
        <item name="android:letterSpacing">0</item>
        <item name="lineHeight">32sp</item>
        <item name="android:textAllCaps">false</item>
    </style>

    <style name="Theme.Toolbar" parent="Widget.Material3.Toolbar.Surface">
        <item name="layout_collapseMode">pin</item>
    </style>

    <style name="Theme.Alert" parent="ThemeOverlay.Material3.MaterialAlertDialog.Centered">
        <item name="android:dialogCornerRadius">@dimen/shape_large_corner</item>
        <item name="materialAlertDialogTitleTextStyle">
            @style/Theme.materialAlertDialogTitleIconStyle
        </item>
        <item name="materialAlertDialogTitleIconStyle">
            @style/Theme.materialAlertDialogTitleTextStyle
        </item>
    </style>

    <style name="Theme.materialAlertDialogTitleIconStyle" parent="@style/MaterialAlertDialog.Material3.Title.Text.CenterStacked">
        <item name="android:layout_marginBottom">16dp</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="Theme.materialAlertDialogTitleTextStyle" parent="@style/MaterialAlertDialog.Material3.Title.Text.CenterStacked">
        
    </style>

    <style name="RoundedImageView">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSize">50%</item>
    </style>

    <style name="Theme.EditText" parent="Widget.Material3.TextInputLayout.FilledBox">
        <item name="boxBackgroundColor">?attr/colorSurfaceContainerHighest</item>
        <item name="boxStrokeWidth">0dp</item>
        <item name="boxStrokeWidthFocused">0dp</item>
        <item name="boxCornerRadiusBottomEnd">@dimen/shape_medium_corner</item>
        <item name="boxCornerRadiusBottomStart">@dimen/shape_medium_corner</item>
        <item name="boxCornerRadiusTopEnd">@dimen/shape_medium_corner</item>
        <item name="boxCornerRadiusTopStart">@dimen/shape_medium_corner</item>
        <item name="hintTextColor">?attr/colorPrimary</item>
        <item name="android:textColorHint">?attr/colorPrimary</item>
    </style>

    <style name="Theme.Progress" parent="Widget.Material3.LinearProgressIndicator">
        <item name="trackThickness">8dp</item>
        <item name="trackCornerRadius">4dp</item>
    </style>

    <style name="Widget.IconWithTextButton" parent="Widget.Material3.Button.IconButton">
        <item name="iconPadding">6dp</item>
        <item name="android:textSize">18sp</item>
        <item name="android:insetTop">0dp</item>
        <item name="android:insetBottom">0dp</item>
        <item name="android:paddingLeft">12dp</item>
        <item name="android:paddingRight">12dp</item>
        <item name="android:minWidth">48dp</item>
        <item name="android:minHeight">48dp</item>
    </style>

</resources>
