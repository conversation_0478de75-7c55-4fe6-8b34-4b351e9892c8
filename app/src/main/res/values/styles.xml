<resources>

    <style name="MyAppTheme" parent="Theme.Main.Light">

    </style>

    <style name="MyAppDialogTheme" parent="Theme.Main.Light">
        <item name="android:radius">@dimen/shape_large_corner</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowBackground">@drawable/bg_grant_permission</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:backgroundDimEnabled">true</item>
    </style>


</resources>