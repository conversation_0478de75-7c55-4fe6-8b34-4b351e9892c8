<resources>
    <string name="app_name">哪吒美式</string>
    <string name="copyright_text">哪吒L车机优化QQ群：636253793   软件及车机安装软件完全免费，请拒绝付费，倒卖狗死全家</string>
    <string name="check_permissions">Check Permission</string>
    <string name="check_permission_text">Check netamade\'s permission to determine it works or not</string>
    <string name="app_debug">调试信息</string>
    <string name="permission_manager_availability">车机应用权限控制</string>
    <string name="exemptions_availability">系统隐藏 API 权限豁免</string>
    <string name="exemptions_availability_success">系统隐藏 API 权限豁免成功</string>
    <string name="exemptions_unavailability">系统隐藏 API 权限豁免失败</string>
    <string name="system_settings_availability">WRITE_SECURE_SETTINGS</string>
    <string name="netamade_require_permission_state">本应用运行所需权限</string>
    <string name="car_state">车机设置状态</string>
    <string name="refresh_data">刷新数据</string>
    <string name="car_location_checked">车机定位</string>
    <string name="car_location_authorized_days">定位授权天数</string>
    <string name="car_access_fine_location_state">ACCESS_FINE_LOCATION</string>
    <string name="car_access_fine_location_state_text">允许您的应用获取确切位置信息</string>
    <string name="car_access_coarse_location_state">ACCESS_COARSE_LOCATION</string>
    <string name="car_access_coarse_location_state_text">允许您的应用仅获取大致位置信息</string>
    <string name="fmt_car_location_authorized_days">剩余 %d 天</string>
    <string name="car_location_checked_text">对应系统设置中的定位授权开关</string>
    <string name="app_debug_text">车机状态调试、应用状态调试</string>
    <string name="launch_map">启动地图导航</string>
    <string name="launch_map_text">自动进行定位授权并打开对应的地图软件</string>
    <string name="baidu_map">\@百度地图</string>
    <string name="amap_map">\@高德地图</string>
    <string name="amap_map_mobile">\@高德地图手机版</string>
    <string name="tencent_map">\@腾讯地图</string>
    <string name="misc">杂项</string>
    <string name="engineer_mode">打开工程模式</string>
    <string name="system_settings_availability_text">修改定位开关以及定位天数所需的权限，需要使用 ADB 授予 WRITE_SECURE_SETTINGS 权限</string>
    <string name="exemptions_availability_text">重启系统定位权限需要，否则无法调用系统 API 进行</string>
    <string name="permission_manager_availability_text">车机权限管理能力，决定能否执行定位授权</string>
    <string name="launch_target_failure">启动目标应用失败</string>
    <string name="launch_target_failure_message">启动目标应用失败，你安装了吗？原因：\n%s</string>
    <string name="launch_intent_failed">启动目标应用失败，你安装了吗！</string>
    <string name="reach_continuous_launch_times">连续打开，再次执行授权操作</string>
    <string name="automotive_swckey_state">车机方控</string>
    <string name="swckey_state">授权时自动解锁方控</string>
    <string name="swckey_state_text">通过无障碍服务，自动打开工程模式中的按键测试，并点击测试按钮，达到解锁车机方向控制权限的效果，解锁方控将在启动目标地图前执行</string>
    <string name="auto_unlock_swckey">自动解锁方控</string>
    <string name="auto_unlock_swckey_text">打开应用或启动地图时自动解锁方控</string>
    <string name="swckey_options">方控选项</string>
    <string name="reward">打赏作者</string>
    <string name="reward_text">作者：奔、吃不饱饿不抱瘦不了——哪吒L车主\n开发不易，全靠兴趣，感谢您对我的工作的支持和赞赏‌\n哪吒L车机优化QQ群：636253793   软件及车机安装软件完全免费，请拒绝付费，倒卖狗死全家。</string>
    <string name="alipay">支付宝</string>
    <string name="wechat">微信</string>
    <string name="close">关闭</string>
    <string name="rewarding">感谢大哥大姐赏饭吃</string>
    <string name="increment_delay">增加延时+</string>
    <string name="decrement_delay">减少延时-</string>
    <string name="current_delay">当前延时：</string>
    <string name="ms">毫秒</string>
    <string name="installed_app">已安装应用</string>
    <string name="loading">加载中......</string>
    <string name="load_user_apps_failed">加载用户应用失败</string>
    <string name="user_apps_list">应用列表</string>
    <string name="user_apps_list_text">获取已安装应用列表</string>
    <string name="auto_grant">自动化授权</string>
    <string name="auto_grant_text">当系统地图启动时、屏幕解锁时、开机时，自动打开三方地图，实现系统导航地图的彻底“替换”</string>
    <string name="auto_grant_target_map">目标地图</string>
    <string name="auto_grant_target_map_text">从列表中选择一个第三方地图用于替换系统地图 %s</string>
    <string name="system_alert_windows_permission_denied">缺少悬浮窗权限 SYSTEM_ALERT_WINDOW</string>
    <string name="system_write_secure_settings_denied">缺少系统设置修改权限 WRITE_SECURE_SETTINGS</string>
    <string name="system_write_secure_settings_granted">已获取到系统设置修改权限 WRITE_SECURE_SETTINGS</string>
    <string name="accessibility_service_description">哪吒美式无障碍服务</string>
    <string name="app_keep_accessibility">无障碍服务列表</string>
    <string name="keeping_accessibility">保活中</string>
    <string name="not_keeping_accessibility">未保活</string>
    <string name="disabled">已禁用</string>
    <string name="enabled">已启用</string>
    <string name="app_keep_accessibility_text">对选定的无障碍服务进行保活，依赖 WRITE_SECURE_SETTINGS 权限</string>
    <string name="any_touch_view_show_background">显示手势触发区域背景</string>
    <string name="any_touch_gesture">侧边栏手势</string>
    <string name="any_touch_gesture_text">通过主屏侧滑手势，实现打开APP、应用返回等操作</string>
    <string name="any_touch_view">侧滑手势</string>
    <string name="any_touch_view_swipe_right">向右滑动</string>
    <string name="any_touch_view_swipe_left">向左滑动</string>
    <string name="any_touch_view_swipe_up">向上滑动</string>
    <string name="any_touch_view_swipe_down">向下滑动</string>
    <string name="any_touch_view_double_tap">双击</string>
    <string name="any_touch_view_long_press">长按</string>
    <string name="auto_map_trigger_event">自动地图触发时机</string>
    <string name="auto_map_trigger_event_text">选中在什么时机启动【目标地图】中选择的地图</string>
    <string name="format_placeholder">%s</string>
    <string name="trigger_last_receiver_safe_time">触发保护时间，不启动目标地图</string>
    <string name="show_in_app_shortcut">显示到应用抽屉</string>
    <string name="not_show_in_app_shortcut">不显示到应用抽屉</string>
    <string name="launch">启动</string>
    <string name="uninstall">卸载</string>
    <string name="more">更多</string>
    <string name="auto_grant_screen_on">屏幕亮起时</string>
    <string name="auto_grant_launch_hozon_map">打开哪吒地图时</string>
    <string name="auto_grant_screen_on_text">在车机屏幕点亮时触发\n测试办法：手动点击【车辆下电】后踩刹车点亮屏幕</string>
    <string name="auto_grant_launch_map">启动目标地图</string>
    <string name="auto_grant_launch_map_text">对应事件触发时，执行完定位授权之后，同时启动选中的目标地图</string>
    <string name="auto_grant_clear_task">清理前台地图</string>
    <string name="auto_grant_clear_task_text">启动目标地图时，将会杀死已在运行的前台地图程序，会影响正在进行中的导航</string>
    <string name="auto_grant_screen_unlock">屏幕解锁时</string>
    <string name="auto_grant_launch_hozon_map_text">在你通过点击主页按钮或者其他方式打开或使用哪吒地图时触发，若短时间内多次进入哪吒地图只会触发一次\n测试方法：点击主屏幕上的主页按键</string>
    <string name="fmt_auto_grant_countdown_seconds">延迟 %d 秒</string>
    <string name="auto_grant_countdown_seconds">触发延时</string>
    <string name="auto_grant_event_enabled">事件触发开关</string>
    <string name="regranting">重新授权中</string>
    <string name="cancel">取消</string>
    <string name="view">查看</string>
    <string name="default_flag">(默认)</string>
    <string name="exception_title">Oops, App 出错了！</string>
    <string name="ok">确定</string>
    <string name="accessibility_service">无障碍服务</string>
    <string name="countdown_execute_tip">长按取消执行\n双击立即执行</string>
    <string name="countdown_execute">执行倒计时</string>
    <string name="countdown_suspended">已暂停</string>
    <string name="grant_now">立即授权</string>
    <string name="launch_now">立即启动</string>
    <string name="grant_now_text">立即执行定位授权，只授权无其他操作，不会启动任何地图</string>
    <string name="countdown_grant">倒计时授权</string>
    <string name="countdown_grant_text">展示一个 180 秒的授权倒计时，立即执行定位授权，只授权无其他操作，不会启动任何地图</string>
    <string name="any_touch_service_offline">无障碍服务已离线，部分功能无法使用</string>
    <string name="any_touch_service_available">无障碍服务已连接，正在为你的小哪吒保驾护航</string>
    <string name="any_touch_service_creating">正在为你的小哪吒保驾护航</string>
    <string name="grant_permission_service_creating">授权服务进行中</string>
    <string name="fmt_current_location_status">系统定位开关: %s 定位授权天数: %d 天</string>
    <string name="all_permission_are_fine">已具备全部运行权限</string>
    <string name="set_location_switch_checked_failed">修改系统定位开关失败</string>
    <string name="set_location_authorized_time_failed">修改系统定位授权天数为 %d 失败, 结果 %d 天</string>
    <string name="set_location_switch_checked_success">修改系统定位开关成功</string>
    <string name="set_location_authorized_time_success">修改系统定位授权时间成功</string>
    <string name="failed_to_revoke_permission">撤回 %s 权限失败: %s</string>
    <string name="failed_to_grant_permission">授予 %s 权限失败: %s</string>
    <string name="grant_permission_success">授予 %s 权限成功</string>
    <string name="revoke_permission_success">撤回 %s 权限成功</string>
    <string name="pause_grant">暂停授权</string>
    <string name="continue_grant">继续授权</string>
    <string name="cancel_grant">取消授权</string>
    <string name="close_now">立即关闭</string>
    <string name="continue_countdown">继续</string>
    <string name="pause">暂停</string>
    <string name="canceled">已取消</string>
    <string name="grant_and_launch_map"><![CDATA[授权 & 启动]]></string>
    <string name="grant_and_launch_map_text">进行定位授权并启动目标地图，请确保你已安装对应的车机版地图（长按可以调整顺序）</string>
    <string name="any_touch_view_trigger_area">手势触发区域</string>
    <string name="fmt_any_touch_view_area_width">触发区域宽度: %d</string>
    <string name="any_touch_view_area_width">触发区域宽度大小</string>
    <string name="any_touch_view_area_height">触发区域高度</string>
    <string name="fmt_any_touch_view_area_height">占据屏幕高度的 %d%%</string>
    <string name="any_touch_view_trigger_area_text">设置手势触发区域显示在屏幕的位置，在屏幕右侧时，左右滑与屏幕左侧相反</string>
    <string name="must_at_least_choice_one">至少选一个</string>

    <string name="customactivityoncrash_error_activity_error_occurred_explanation">Oops, App 出错了！请向开发者反馈以帮助修复。</string>
    <string name="customactivityoncrash_error_activity_restart_app">重启应用</string>
    <string name="customactivityoncrash_error_activity_close_app">关闭应用</string>
    <string name="customactivityoncrash_error_activity_error_details">查看错误详情</string>
    <string name="customactivityoncrash_error_activity_error_details_title">错误详情</string>
    <string name="customactivityoncrash_error_activity_error_details_close">关闭</string>
    <string name="customactivityoncrash_error_activity_error_details_copy">复制到剪贴板</string>
    <string name="customactivityoncrash_error_activity_error_details_copied">已复制到剪贴板</string>
    <string name="customactivityoncrash_error_activity_error_details_clipboard_label">错误信息</string>
    <string name="do_not_revoke_before_grant">定位授权时不撤回权限</string>
    <string name="experimental_feature">实验性功能</string>
    <string name="no_location_permission">无定位权限</string>
    <string name="satellite_count">卫星数量</string>
    <string name="re_grant_because_no_satellite">卫星信号弱，重新授权定位</string>
    <string name="show_user_app">显示用户应用</string>
    <string name="show_system_app">显示系统应用</string>
    <string name="show_not_activities_app">显示无启动项的应用</string>
    <string name="app_summary">总应用: %d 系统应用: %d 用户应用: %d 无启动项: %d 当前列表: %d</string>
    <string name="failed_to_update_swckey_state">设置方控状态失败: %s</string>
    <string name="failed_to_update_loud_voice">设置哪吒大嗓门失败: %s</string>
    <string name="swckey_state_auto_unlock">解锁方控</string>
    <string name="swckey_state_auto_unlock_text">通过自动化点击操作，解锁方控</string>
    <string name="swckey_state_auto_lock_text">通过自动化点击操作，恢复方控到默认状态</string>
    <string name="swckey_state_auto_lock">恢复方控</string>
    <string name="syslog">系统日志</string>
    <string name="syslog_enabled">日志抓取开关</string>
    <string name="clear_syslog">清空日志</string>
    <string name="auto_loudvoice">自动打开哪吒大嗓门</string>
    <string name="auto_loudvoice_enabled">打开大嗓门</string>
    <string name="auto_loudvoice_enabled_text">自动化打开哪吒大嗓门</string>
    <string name="auto_loudvoice_disabled">关闭大嗓门</string>
    <string name="auto_loudvoice_disabled_text">自动化关闭哪吒大嗓门</string>
    <string name="car_hooks">Car Hooks</string>
    <string name="app_icon_editor">图标配置</string>
    <string name="xposed">Xposed</string>
    <string name="xposed_description">以下功能全部依赖 ROOT + Xposed 框架才能运行</string>
    <string name="xposed_restart_main_launcher">重启【主屏桌面】</string>
    <string name="xposed_restart_main_launcher_text">重新启动驾驶位主屏幕的桌面</string>
    <string name="xposed_restart_vice_launcher_text">重新启动副驾驶位屏幕的桌面</string>
    <string name="xposed_restart_vice_launcher">重启【副屏桌面】</string>
    <string name="xposed_reset_main_launcher_text">重置【主屏桌面】的图标布局</string>
    <string name="xposed_reset_vice_launcher_text">重置【副屏桌面】的图标布局</string>
    <string name="xposed_reset_main_launcher">删除【主屏】现有的桌面布局和Dock布局并重启桌面</string>
    <string name="xposed_reset_vice_launcher">删除【副屏】现有的桌面布局和Dock布局并重启桌面</string>
    <string name="xposed_misc">杂项</string>
    <string name="xposed_main">主屏幕 (以下功能全部依赖 ROOT + Xposed 框架才能运行)</string>
    <string name="hook_extend_launcher">主页🏠按钮长按返回\n系统🚗按钮长按重启桌面\n应用抽屉按钮长按打开哪吒美式</string>
    <string name="hook_show_all_user_apps_text">自动添加所有用户应用到桌面图标中，额外包括工程模式，修改应用后需要【重置桌面布局】</string>
    <string name="xposed_vice">副驾驶屏幕 (以下功能全部依赖 ROOT + Xposed 框架才能运行)</string>
    <string name="restart_systemui">重启系统用户界面</string>
    <string name="hook_add_back_icon">向底部导航栏增加一个返回图标，单击返回，长按可以杀死前台应用</string>
    <string name="secondary_display_any_touch_view_enabled">副屏手势 (ROOT)</string>
    <string name="block_aispeech_network_text">屏幕亮起时，同时禁用哪吒语言的联网权限，使用本地语音</string>
    <string name="block_aispeech_network">禁用哪吒语音联网 (ROOT)</string>
    <string name="unblock_aispeech_network">允许哪吒语音联网 (ROOT)</string>
    <string name="adb_over_wifi">无线 ADB (ROOT)</string>
</resources>
