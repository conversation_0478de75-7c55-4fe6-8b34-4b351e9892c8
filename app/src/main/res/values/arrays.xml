<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string-array name="auto_map_entries">
        <item>禁用</item>
        <item>@string/baidu_map</item>
        <item>@string/tencent_map</item>
        <item>@string/amap_map</item>
        <item>@string/amap_map_mobile</item>
    </string-array>
    <string-array name="auto_map_values">
        <item>none</item>
        <item>\@baidu_map</item>
        <item>\@tencent_map</item>
        <item>\@a_map</item>
        <item>\@a_map_mobile</item>
    </string-array>

    <string-array name="any_touch_view_swipe_entries">
        <item>禁用</item>
        <item>返回</item>
        <item>应用抽屉</item>
        <item>主页</item>
        <item>@string/app_name</item>
        <item>哪吒地图</item>
        <item>@string/baidu_map</item>
        <item>@string/tencent_map</item>
        <item>@string/amap_map</item>
        <item>@string/amap_map_mobile</item>
    </string-array>
    <string-array name="any_touch_view_swipe_entries_values">
        <item>none</item>
        <item>back</item>
        <item>app_shortcut</item>
        <item>home</item>
        <item>@string/package_name</item>
        <item>hozon_map</item>
        <item>\@baidu_map</item>
        <item>\@tencent_map</item>
        <item>\@a_map</item>
        <item>\@a_map_mobile</item>
    </string-array>

    <string-array name="any_touch_view_trigger_area_entries">
        <item>屏幕左侧</item>
        <item>屏幕右侧</item>
    </string-array>
    <string-array name="any_touch_view_trigger_area_default">
        <item>left</item>
        <item>right</item>
    </string-array>
    <string-array name="any_touch_view_trigger_area_values">
        <item>left</item>
        <item>right</item>
    </string-array>
    <string-array name="satellite_auto_grant_entries" >
        <item>禁用</item>
        <item>1 秒</item>
        <item>3 秒</item>
        <item>5 秒</item>
        <item>10 秒</item>
        <item>20 秒</item>
        <item>30 秒</item>
        <item>40 秒</item>
        <item>50 秒</item>
        <item>60 秒</item>
    </string-array>
    <string-array name="satellite_auto_grant_values" >
        <item>none</item>
        <item>1</item>
        <item>3</item>
        <item>5</item>
        <item>10</item>
        <item>20</item>
        <item>30</item>
        <item>40</item>
        <item>50</item>
        <item>60</item>
    </string-array>
</resources>