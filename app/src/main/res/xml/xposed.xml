<?xml version="1.0" encoding="utf-8"?>
<PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:summary="@string/xposed_description"
    android:title="@string/xposed">

    <io.github.netamade.ui.preference.ExpandablePreferenceCategory
        android:title="杂项"
        app:iconSpaceReserved="false">

        <PreferenceScreen
            android:key="open_lsposed"
            android:summary="打开 LSPosed 界面"
            android:title="LSPosed"
            app:iconSpaceReserved="false" />

        <PreferenceScreen
            android:key="xposed_restart_systemui"
            android:summary="killall -9 com.hozonauto.systemui"
            android:title="@string/restart_systemui"
            app:iconSpaceReserved="false" />

    </io.github.netamade.ui.preference.ExpandablePreferenceCategory>


    <io.github.netamade.ui.preference.ExpandablePreferenceCategory
        android:key="category_xposed_main"
        android:title="@string/xposed_main"
        app:alwaysCollapse="false"
        app:childKeys="hook_extend_launcher_icon_main,hook_add_back_icon_main,show_all_user_apps_main,xposed_restart_main_launcher,xposed_reset_main_launcher"
        app:iconSpaceReserved="false">

        <SwitchPreferenceCompat
            android:key="hook_extend_launcher_icon_main"
            android:summary="@string/hook_extend_launcher"
            android:switchTextOff="@string/disabled"
            android:switchTextOn="@string/enabled"
            android:title="主屏图标功能拓展"
            app:defaultValue="true"
            app:iconSpaceReserved="false" />

        <SwitchPreferenceCompat
            android:key="hook_add_back_icon_main"
            android:summary="@string/hook_add_back_icon"
            android:switchTextOff="@string/disabled"
            android:switchTextOn="@string/enabled"
            android:title="主屏注入【返回】图标"
            app:defaultValue="true"
            app:iconSpaceReserved="false" />

        <SwitchPreferenceCompat
            android:key="show_all_user_apps_main"
            android:summary="@string/hook_show_all_user_apps_text"
            android:switchTextOff="@string/disabled"
            android:switchTextOn="@string/enabled"
            android:title="主屏抽屉中显示所有用户应用"
            app:defaultValue="true"
            app:iconSpaceReserved="false" />

        <PreferenceScreen
            android:key="xposed_restart_main_launcher"
            android:summary="@string/xposed_restart_main_launcher_text"
            android:title="@string/xposed_restart_main_launcher"
            app:iconSpaceReserved="false" />

        <PreferenceScreen
            android:key="xposed_reset_main_launcher"
            android:summary="@string/xposed_reset_main_launcher_text"
            android:title="@string/xposed_reset_main_launcher"
            app:iconSpaceReserved="false" />

    </io.github.netamade.ui.preference.ExpandablePreferenceCategory>

    <io.github.netamade.ui.preference.ExpandablePreferenceCategory
        android:key="category_xposed_vice"
        android:title="@string/xposed_vice"
        app:alwaysCollapse="false"
        app:childKeys="hook_extend_launcher_icon_vice,hook_add_back_icon_vice,show_all_user_apps_vice,xposed_restart_vice_launcher,xposed_reset_vice_launcher"
        app:iconSpaceReserved="false">

        <SwitchPreferenceCompat
            android:key="hook_extend_launcher_icon_vice"
            android:summary="@string/hook_extend_launcher"
            android:switchTextOff="@string/disabled"
            android:switchTextOn="@string/enabled"
            android:title="副屏图标功能拓展"
            app:defaultValue="true"
            app:iconSpaceReserved="false" />

        <SwitchPreferenceCompat
            android:key="hook_add_back_icon_vice"
            android:summary="@string/hook_add_back_icon"
            android:switchTextOff="@string/disabled"
            android:switchTextOn="@string/enabled"
            android:title="副屏注入【返回】图标"
            app:defaultValue="true"
            app:iconSpaceReserved="false" />

        <SwitchPreferenceCompat
            android:key="show_all_user_apps_vice"
            android:summary="@string/hook_show_all_user_apps_text"
            android:switchTextOff="@string/disabled"
            android:switchTextOn="@string/enabled"
            android:title="副屏抽屉中显示所有用户应用"
            app:defaultValue="true"
            app:iconSpaceReserved="false" />


        <PreferenceScreen
            android:key="xposed_restart_vice_launcher"
            android:summary="@string/xposed_restart_vice_launcher_text"
            android:title="@string/xposed_restart_vice_launcher"
            app:iconSpaceReserved="false" />

        <PreferenceScreen
            android:key="xposed_reset_vice_launcher"
            android:summary="@string/xposed_reset_vice_launcher_text"
            android:title="@string/xposed_reset_vice_launcher"
            app:iconSpaceReserved="false" />

    </io.github.netamade.ui.preference.ExpandablePreferenceCategory>

    <io.github.netamade.ui.preference.ExpandablePreferenceCategory
        android:key="category_xposed_permission"
        android:title="权限管控"
        app:alwaysCollapse="false"
        app:childKeys="hook_power_manager_whitelist,hook_clear_hozon_permissions_control,hook_restart_gps_service_screen_on"
        app:iconSpaceReserved="false">

        <SwitchPreferenceCompat
            android:key="hook_power_manager_whitelist"
            android:summary="添加白名单到 PowerManagerService.mShutdownWhiteList"
            android:switchTextOff="@string/disabled"
            android:switchTextOn="@string/enabled"
            android:title="地图软件注入电源管理白名单"
            app:defaultValue="true"
            app:iconSpaceReserved="false" />

        <SwitchPreferenceCompat
            android:key="hook_clear_hozon_permissions_control"
            android:summary="清空 HozonPermissionManager.mControlledPermissionList"
            android:switchTextOff="@string/disabled"
            android:switchTextOn="@string/enabled"
            android:title="定位权限摆脱哪吒控制"
            app:defaultValue="true"
            app:iconSpaceReserved="false" />

        <SwitchPreferenceCompat
            android:key="hook_restart_gps_service_screen_on"
            android:summary="需要配合注入【返回】图标使用"
            android:switchTextOff="@string/disabled"
            android:switchTextOn="@string/enabled"
            android:title="亮屏时自动重启 GPS 服务"
            app:defaultValue="true"
            app:iconSpaceReserved="false" />

    </io.github.netamade.ui.preference.ExpandablePreferenceCategory>

    <io.github.netamade.ui.preference.ExpandablePreferenceCategory
        android:key="category_xposed_utility"
        android:title="体验增强"
        app:alwaysCollapse="false"
        app:childKeys="hook_long_press_kill,block_user_app_notification_sound,three_finger_gesture_back,block_user_app_notification_heads_up"
        app:iconSpaceReserved="false">

        <SwitchPreferenceCompat
            android:key="hook_long_press_kill"
            android:summary="需要配合注入【返回】图标使用"
            android:switchTextOff="@string/disabled"
            android:switchTextOn="@string/enabled"
            android:title="长按返回键杀死前台应用"
            app:defaultValue="true"
            app:iconSpaceReserved="false" />

        <SwitchPreferenceCompat
            android:key="block_user_app_notification_sound"
            android:summary="不会再被用户应用的通知声音打扰"
            android:switchTextOff="@string/disabled"
            android:switchTextOn="@string/enabled"
            android:title="屏蔽用户应用的通知声音"
            app:defaultValue="true"
            app:iconSpaceReserved="false" />


        <SwitchPreferenceCompat
            android:key="block_user_app_notification_heads_up"
            android:summary="不会再被用户应用的弹出通知打扰"
            android:switchTextOff="@string/disabled"
            android:switchTextOn="@string/enabled"
            android:title="屏蔽用户应用的通知弹出"
            app:defaultValue="true"
            app:iconSpaceReserved="false" />

        <SwitchPreferenceCompat
            android:key="three_finger_gesture_back"
            android:summary="替换系统三指返回主页的手势为返回"
            android:switchTextOff="@string/disabled"
            android:switchTextOn="@string/enabled"
            android:title="系统级三指手势触发返回"
            app:defaultValue="true"
            app:iconSpaceReserved="false" />

    </io.github.netamade.ui.preference.ExpandablePreferenceCategory>
</PreferenceScreen>
