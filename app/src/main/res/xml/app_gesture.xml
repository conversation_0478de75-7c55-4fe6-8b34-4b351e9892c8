<?xml version="1.0" encoding="utf-8"?>
<PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">
    <SwitchPreferenceCompat
        android:key="any_touch_view_enabled"
        android:title="@string/any_touch_view"
        app:defaultValue="true"
        app:iconSpaceReserved="false" />

    <SwitchPreferenceCompat
        android:dependency="any_touch_view_enabled"
        android:key="secondary_display_any_touch_view_enabled"
        android:title="@string/secondary_display_any_touch_view_enabled"
        app:defaultValue="true"
        app:iconSpaceReserved="false" />


    <SwitchPreferenceCompat
        android:dependency="any_touch_view_enabled"
        android:key="any_touch_view_show_background"
        android:title="@string/any_touch_view_show_background"
        app:defaultValue="true"
        app:iconSpaceReserved="false" />

    <MultiSelectListPreference
        android:dependency="any_touch_view_enabled"
        android:entries="@array/any_touch_view_trigger_area_entries"
        android:entryValues="@array/any_touch_view_trigger_area_values"
        android:key="any_touch_view_trigger_area"
        android:summary="@string/any_touch_view_trigger_area_text"
        android:title="@string/any_touch_view_trigger_area"
        app:defaultValue="@array/any_touch_view_trigger_area_default"
        app:iconSpaceReserved="false" />


    <io.github.netamade.ui.preference.IndicatedSeekBarPreference
        android:dependency="any_touch_view_enabled"
        android:key="any_touch_view_area_width"
        android:max="600"
        android:summary="@string/fmt_any_touch_view_area_width"
        android:title="@string/any_touch_view_area_width"
        app:defaultValue="60"
        app:iconSpaceReserved="false"
        app:min="10"
        app:showSeekBarValue="true" />

    <io.github.netamade.ui.preference.IndicatedSeekBarPreference
        android:dependency="any_touch_view_enabled"
        android:key="any_touch_view_area_height"
        android:max="100"
        android:summary="@string/fmt_any_touch_view_area_height"
        android:title="@string/any_touch_view_area_height"
        app:defaultValue="70"
        app:iconSpaceReserved="false"
        app:min="10"
        app:showSeekBarValue="true" />

    <ListPreference
        android:dependency="any_touch_view_enabled"
        android:entries="@array/any_touch_view_swipe_entries"
        android:entryValues="@array/any_touch_view_swipe_entries_values"
        android:key="any_touch_view_swipe_right"
        android:summary="@string/format_placeholder"
        android:title="@string/any_touch_view_swipe_right"
        app:defaultValue="back"
        app:iconSpaceReserved="false" />

    <ListPreference
        android:dependency="any_touch_view_enabled"
        android:entries="@array/any_touch_view_swipe_entries"
        android:entryValues="@array/any_touch_view_swipe_entries_values"
        android:key="any_touch_view_swipe_up"
        android:summary="@string/format_placeholder"
        android:title="@string/any_touch_view_swipe_up"
        app:defaultValue="app_shortcut"
        app:iconSpaceReserved="false" />

    <ListPreference
        android:dependency="any_touch_view_enabled"
        android:entries="@array/any_touch_view_swipe_entries"
        android:entryValues="@array/any_touch_view_swipe_entries_values"
        android:key="any_touch_view_swipe_down"
        android:summary="@string/format_placeholder"
        android:title="@string/any_touch_view_swipe_down"
        app:defaultValue="app_shortcut"
        app:iconSpaceReserved="false" />

    <ListPreference
        android:dependency="any_touch_view_enabled"
        android:entries="@array/any_touch_view_swipe_entries"
        android:entryValues="@array/any_touch_view_swipe_entries_values"
        android:key="any_touch_view_double_tap"
        android:summary="@string/format_placeholder"
        android:title="@string/any_touch_view_double_tap"
        app:defaultValue="hozon_map"
        app:iconSpaceReserved="false" />

    <ListPreference
        android:dependency="any_touch_view_enabled"
        android:entries="@array/any_touch_view_swipe_entries"
        android:entryValues="@array/any_touch_view_swipe_entries_values"
        android:key="any_touch_view_long_press"
        android:summary="@string/format_placeholder"
        android:title="@string/any_touch_view_long_press"
        app:defaultValue="@string/package_name"
        app:iconSpaceReserved="false" />

    <ListPreference
        android:dependency="any_touch_view_enabled"
        android:entries="@array/any_touch_view_swipe_entries"
        android:entryValues="@array/any_touch_view_swipe_entries_values"
        android:key="any_touch_view_swipe_left"
        android:summary="@string/format_placeholder"
        android:title="@string/any_touch_view_swipe_left"
        app:defaultValue="none"
        app:iconSpaceReserved="false" />

</PreferenceScreen>
