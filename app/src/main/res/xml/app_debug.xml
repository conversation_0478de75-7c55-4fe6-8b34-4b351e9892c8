<?xml version="1.0" encoding="utf-8"?>
<PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <PreferenceScreen
        android:key="refresh_data"
        android:summary="@string/copyright_text"
        android:title="@string/refresh_data"
        app:iconSpaceReserved="false" />

    <io.github.netamade.ui.preference.ExpandablePreferenceCategory
        android:title="@string/netamade_require_permission_state"
        app:childKeys="system_settings_availability,exemptions_availability,permission_manager_availability"
        app:iconSpaceReserved="false">

        <SwitchPreferenceCompat
            android:key="system_settings_availability"
            android:summary="@string/system_settings_availability_text"
            android:title="@string/system_settings_availability"
            app:iconSpaceReserved="false" />

        <SwitchPreferenceCompat
            android:key="exemptions_availability"
            android:summary="@string/exemptions_availability_text"
            android:title="@string/exemptions_availability"
            app:iconSpaceReserved="false" />

        <SwitchPreferenceCompat
            android:key="permission_manager_availability"
            android:summary="@string/permission_manager_availability_text"
            android:title="@string/permission_manager_availability"
            app:iconSpaceReserved="false" />

    </io.github.netamade.ui.preference.ExpandablePreferenceCategory>

    <io.github.netamade.ui.preference.ExpandablePreferenceCategory
        android:title="@string/car_state"
        app:childKeys="car_location_checked,car_location_authorized_days,car_access_fine_location_state,car_access_coarse_location_state"
        app:iconSpaceReserved="false">

        <SwitchPreferenceCompat
            android:key="car_location_checked"
            android:summary="@string/car_location_checked_text"
            android:title="@string/car_location_checked"
            app:iconSpaceReserved="false" />

        <io.github.netamade.ui.preference.IndicatedSeekBarPreference
            android:key="car_location_authorized_days"
            android:max="364"
            android:summary="@string/fmt_car_location_authorized_days"
            android:title="@string/car_location_authorized_days"
            app:iconSpaceReserved="false"
            app:min="0"
            app:showSeekBarValue="true" />

        <SwitchPreferenceCompat
            android:key="car_access_fine_location_state"
            android:summary="@string/car_access_fine_location_state_text"
            android:title="@string/car_access_fine_location_state"
            app:iconSpaceReserved="false" />

        <SwitchPreferenceCompat
            android:key="car_access_coarse_location_state"
            android:summary="@string/car_access_coarse_location_state_text"
            android:title="@string/car_access_coarse_location_state"
            app:iconSpaceReserved="false" />
    </io.github.netamade.ui.preference.ExpandablePreferenceCategory>
</PreferenceScreen>