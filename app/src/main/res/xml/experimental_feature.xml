<?xml version="1.0" encoding="utf-8"?>
<PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <SwitchPreferenceCompat
        android:key="launch_no_clear_task"
        android:switchTextOff="@string/disabled"
        android:switchTextOn="@string/enabled"
        android:title="启动其他应用时，不影响已在运行的界面"
        app:defaultValue="true"
        app:iconSpaceReserved="false" />

    <PreferenceCategory
        android:key="category_system_map"
        android:title="系统地图"
        app:alwaysCollapse="true"
        app:childKeys="hijack_system_map_opening_target_map"
        app:iconSpaceReserved="false">

        <ListPreference
            android:entries="@array/auto_map_entries"
            android:entryValues="@array/auto_map_values"
            android:key="hijack_system_map_opening_target_map"
            android:summary="当打开系统地图时, 自动切换下方到选中的目标地图, 但不进行定位授权\n当前: %s"
            android:title="劫持系统地图"
            app:defaultValue="none"
            app:iconSpaceReserved="false" />

        <Preference
            android:key="hijack_system_map_opening_once"
            android:summary="临时打开系统地图，不会触发劫持"
            android:title="打开系统地图"
            app:defaultValue="none"
            app:iconSpaceReserved="false" />

    </PreferenceCategory>

    <PreferenceCategory
        android:key="category_syslog"
        android:title="系统日志抓取"
        app:alwaysCollapse="true"
        app:childKeys=""
        app:iconSpaceReserved="false">

        <Preference
            android:key="syslog_show_dialog"
            android:summary="实时抓取系统日志"
            android:title="抓取日志"
            app:defaultValue="none"
            app:iconSpaceReserved="false" />

    </PreferenceCategory>

    <PreferenceCategory
        android:key="category_swckey_state"
        android:title="@string/automotive_swckey_state"
        app:alwaysCollapse="true"
        app:childKeys=""
        app:iconSpaceReserved="false">

        <Preference
            android:key="swckey_state_auto_unlock"
            android:summary="@string/swckey_state_auto_unlock_text"
            android:title="@string/swckey_state_auto_unlock"
            app:defaultValue="none"
            app:iconSpaceReserved="false" />

        <Preference
            android:key="swckey_state_auto_lock"
            android:summary="@string/swckey_state_auto_lock_text"
            android:title="@string/swckey_state_auto_lock"
            app:defaultValue="none"
            app:iconSpaceReserved="false" />

    </PreferenceCategory>

    <PreferenceCategory
        android:key="category_loudvoice"
        android:title="@string/auto_loudvoice"
        app:alwaysCollapse="true"
        app:childKeys=""
        app:iconSpaceReserved="false">

        <Preference
            android:key="auto_loudvoice_enabled"
            android:summary="@string/auto_loudvoice_enabled_text"
            android:title="@string/auto_loudvoice_enabled"
            app:defaultValue="none"
            app:iconSpaceReserved="false" />

        <Preference
            android:key="auto_loudvoice_disabled"
            android:summary="@string/auto_loudvoice_disabled_text"
            android:title="@string/auto_loudvoice_disabled"
            app:defaultValue="none"
            app:iconSpaceReserved="false" />

    </PreferenceCategory>

    <PreferenceCategory
        android:key="category_auto_grant_screen_on"
        android:title="授权相关"
        app:alwaysCollapse="true"
        app:childKeys="auto_grant_do_not_revoke_before_grant,satellite_view_enabled,satellite_auto_grant_seconds"
        app:iconSpaceReserved="false">

        <SwitchPreferenceCompat
            android:enabled="false"
            android:key="auto_grant_do_not_revoke_before_grant"
            android:summary="跳过授权时的【撤回授权】步骤，目前已知跳过会导致地图授权失败"
            android:switchTextOff="@string/disabled"
            android:switchTextOn="@string/enabled"
            android:title="@string/do_not_revoke_before_grant"
            app:defaultValue="false"
            app:iconSpaceReserved="false" />
    </PreferenceCategory>

    <PreferenceCategory
        android:enabled="false"
        android:key="satellite_monitor_category"
        android:title="实时卫星监控"
        app:alwaysCollapse="true"
        app:childKeys="auto_grant_do_not_revoke_before_grant,satellite_view_enabled,satellite_auto_grant_seconds"
        app:iconSpaceReserved="false">

        <SwitchPreferenceCompat
            android:dependency="satellite_monitor_category"
            android:key="satellite_view_enabled"
            android:summary="显示一个实时显示卫星数量的悬浮窗, 并在卫星信号弱, 重新授权定位"
            android:switchTextOff="@string/disabled"
            android:switchTextOn="@string/enabled"
            android:title="悬浮窗"
            app:defaultValue="false"
            app:iconSpaceReserved="false" />

        <ListPreference
            android:dependency="satellite_monitor_category"
            android:entries="@array/satellite_auto_grant_entries"
            android:entryValues="@array/satellite_auto_grant_values"
            android:key="satellite_auto_grant_seconds"
            android:summary="若 %s 秒内无卫星数量，将自动触发重新授权"
            android:switchTextOff="@string/disabled"
            android:switchTextOn="@string/enabled"
            android:title="自动授权"
            app:defaultValue="none"
            app:iconSpaceReserved="false" />

    </PreferenceCategory>
</PreferenceScreen>
