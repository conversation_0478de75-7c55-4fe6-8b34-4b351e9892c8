<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:alpha="0.5"
    android:background="@color/black"
    android:padding="3dp">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ic_anytouch"
        android:layout_width="100dp"
        android:layout_height="100dp"
        android:layout_above="@id/text"
        android:scaleType="centerCrop"
        android:src="@drawable/ic_back_light"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@id/ic_anytouch"
        android:text="@string/app_name"
        android:textColor="#FFFFFF"
        android:textSize="20sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ic_anytouch" />

</androidx.constraintlayout.widget.ConstraintLayout>
