<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_alignParentStart="true"
        android:layout_toStartOf="@+id/icon"
        android:layout_weight="1"
        android:orientation="vertical">

        <com.google.android.material.textview.MaterialTextView
            android:id="@android:id/title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="?android:attr/colorAccent"
            android:textAppearance="?android:attr/textAppearanceMedium" />

        <com.google.android.material.textview.MaterialTextView
            android:id="@android:id/summary"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textAppearance="?android:attr/textAppearanceSmall" />
    </LinearLayout>

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/icon"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:scaleType="centerCrop" />

</RelativeLayout>
