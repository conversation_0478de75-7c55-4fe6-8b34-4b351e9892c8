<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingStart="95dp">

    <com.google.android.material.divider.MaterialDivider
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingStart="@dimen/shape_margin_medium"
        android:paddingEnd="@dimen/shape_margin_medium">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/activityIcon"
            android:layout_width="@dimen/app_icon_size_small"
            android:layout_height="@dimen/app_icon_size_small"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            app:srcCompat="@drawable/ic_unknown" />

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/defaultShortcut"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginStart="5dp"
            android:layout_marginEnd="5dp"
            android:layout_toEndOf="@+id/activityIcon"
            android:maxLines="1"
            android:minWidth="10dp"
            android:text="@string/default_flag"
            android:textStyle="bold"
            android:visibility="gone" />

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/activityLabel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginStart="5dp"
            android:layout_toEndOf="@+id/defaultShortcut"
            android:ellipsize="marquee"
            android:maxLines="1"
            android:text="@string/package_name"
            android:textSize="@dimen/textSizeAction"
            android:textStyle="bold" />

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/activityName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_marginStart="10dp"
            android:layout_toStartOf="@id/showInAppShortcut"
            android:layout_toEndOf="@id/activityLabel"
            android:text="@string/package_name"
            android:textSize="14sp"
            android:typeface="monospace" />

        <com.google.android.material.checkbox.MaterialCheckBox
            android:id="@+id/showInAppShortcut"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:layout_toStartOf="@id/launchApp"
            android:checked="false"
            android:text="@string/show_in_app_shortcut"
            android:textSize="16sp" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/launchApp"
            style="@style/Widget.IconWithTextButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:layout_marginStart="3dp"
            android:text="@string/launch"
            android:textColor="@color/colorPrimaryDark"
            app:icon="@drawable/ic_launch"
            app:iconTint="@color/colorPrimaryDark" />
    </RelativeLayout>
</LinearLayout>