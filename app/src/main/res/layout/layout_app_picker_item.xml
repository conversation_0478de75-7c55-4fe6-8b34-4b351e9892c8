<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="12dp"
    android:background="?android:attr/selectableItemBackground">

    <ImageView
        android:id="@+id/appIcon"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginEnd="12dp"
        android:scaleType="centerCrop" />

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/appName"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="App Name"
                android:textSize="16sp"
                android:textStyle="bold" />

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/systemIndicator"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="系统"
                android:textSize="12sp"
                android:background="@android:color/holo_orange_light"
                android:padding="4dp"
                android:visibility="gone" />

        </LinearLayout>

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/packageName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="com.example.package"
            android:textSize="14sp"
            android:textColor="?android:attr/textColorSecondary" />

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/activityName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="com.example.MainActivity"
            android:textSize="12sp"
            android:textColor="?android:attr/textColorTertiary"
            android:visibility="gone" />

    </LinearLayout>

</LinearLayout>
