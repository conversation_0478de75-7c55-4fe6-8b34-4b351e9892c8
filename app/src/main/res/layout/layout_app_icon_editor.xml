<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <!-- Top buttons container -->
    <LinearLayout
        android:id="@+id/topButtonsContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <!-- First row: Load and Save buttons -->
        <LinearLayout
            android:id="@+id/topButtonsLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:orientation="horizontal">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/loadLayoutButton"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:layout_marginEnd="4dp"
                android:layout_weight="1"
                android:text="加载布局文件" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/saveLayoutButton"
                style="@style/Widget.Material3.Button"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:layout_marginEnd="4dp"
                android:layout_weight="1"
                android:text="保存布局文件" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/restartMainLauncherButton"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:layout_marginEnd="4dp"
                android:layout_weight="1"
                android:text="重启主屏桌面"
                android:textSize="12sp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/restartViceLauncherButton"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:layout_marginEnd="4dp"
                android:layout_weight="1"
                android:text="重启副屏桌面"
                android:textSize="12sp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/resetMainLayoutButton"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:layout_marginEnd="4dp"
                android:layout_weight="1"
                android:text="重置主屏布局"
                android:textSize="12sp" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/resetViceLayoutButton"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:layout_weight="1"
                android:text="重置副屏布局"
                android:textSize="12sp" />

        </LinearLayout>

    </LinearLayout>

    <!-- RecyclerView for app icons -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/topButtonsContainer" />

    <!-- Floating Action Button for adding apps -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fabAddApp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:src="@android:drawable/ic_input_add"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>