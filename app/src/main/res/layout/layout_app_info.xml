<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="100dp"
        android:background="?selectableItemBackground"
        android:clickable="true"
        android:id="@+id/rootLayout"
        android:focusable="true"
        android:foreground="?selectableItemBackgroundBorderless"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingStart="@dimen/shape_margin_medium"
        android:paddingEnd="@dimen/shape_margin_medium">

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/appIcon"
            android:layout_width="@dimen/app_icon_size"
            android:layout_height="@dimen/app_icon_size"
            android:layout_margin="@dimen/shape_margin_medium"
            android:gravity="center_vertical"
            app:srcCompat="@drawable/ic_unknown" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="7dp"
            android:layout_weight="1"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <com.google.android.material.textview.MaterialTextView
                    android:id="@+id/appName"
                    android:layout_width="0.0dip"
                    android:layout_height="wrap_content"
                    android:layout_weight="1.0"
                    android:ellipsize="end"
                    android:singleLine="true"
                    android:text="@string/package_name"
                    android:textSize="@dimen/textSizeBig"
                    android:textStyle="bold" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <com.google.android.material.textview.MaterialTextView
                    android:id="@+id/packageName"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/app_info_item_height"
                    android:text="@string/package_name"
                    android:typeface="monospace" />

                <com.google.android.material.textview.MaterialTextView
                    android:id="@+id/version"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/app_info_item_height"
                    android:text="@string/package_name" />

            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:padding="5dp">


            <com.google.android.material.button.MaterialButton
                android:id="@+id/uninstallApp"
                style="@style/Widget.IconWithTextButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="3dp"
                android:text="@string/uninstall"
                android:textColor="@color/colorError"
                app:icon="@drawable/ic_uninstall"
                app:iconTint="@color/colorError" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/more"
                style="@style/Widget.IconWithTextButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="3dp"
                android:text="@string/more"
                android:textColor="@color/black"
                app:icon="@drawable/ic_more"
                app:iconTint="@color/black" />
        </LinearLayout>

        <com.google.android.material.imageview.ShapeableImageView
            android:id="@+id/expandIcon"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:scaleType="centerCrop"
            android:src="@drawable/ic_collapsed" />

    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />
</LinearLayout>
