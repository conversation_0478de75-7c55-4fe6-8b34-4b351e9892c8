<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?android:attr/selectableItemBackground"
    android:gravity="center"
    android:orientation="vertical"
    android:padding="8dp">

    <ImageView
        android:id="@+id/appIcon"
        android:layout_width="64dp"
        android:layout_height="64dp"
        android:layout_marginBottom="4dp"
        android:scaleType="centerCrop" />

    <com.google.android.material.textview.MaterialTextView
        android:id="@+id/appName"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:gravity="center"
        android:maxLines="2"
        android:text="App Name"
        android:textSize="12sp" />

</LinearLayout>
