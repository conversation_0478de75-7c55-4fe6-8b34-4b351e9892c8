<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?selectableItemBackground"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?selectableItemBackgroundBorderless"
    android:orientation="horizontal"
    android:paddingStart="@dimen/shape_margin_medium"
    android:paddingEnd="@dimen/shape_margin_medium">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/icon"
        android:layout_width="35dp"
        android:layout_height="35dp"
        android:layout_margin="@dimen/shape_margin_medium"
        android:gravity="center_vertical"
        app:srcCompat="@drawable/ic_directory" />

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_marginStart="7dp"
        android:layout_weight="1"
        android:orientation="vertical">

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/pathName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:singleLine="true"
            android:text="预设路径名称"
            android:textSize="18sp"
            android:textStyle="bold" />

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/pathValue"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:singleLine="false"
            android:text="预设路径值"
            android:textSize="14sp"
            android:typeface="monospace" />

    </LinearLayout>

</LinearLayout>
