<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:id="@+id/app_bar_parent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/colorPrimary"
        android:orientation="vertical"
        android:paddingTop="40dp"
        app:layout_constraintBottom_toTopOf="@id/content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <RelativeLayout
            android:id="@+id/app_bar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:elevation="2dp"
            android:padding="10dp">

            <com.google.android.material.button.MaterialButton
                android:id="@+id/back"
                android:layout_width="60dp"
                android:layout_height="60dp"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true"
                app:icon="@drawable/ic_back_light" />

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginStart="10dp"
                android:layout_toStartOf="@id/me"
                android:layout_toEndOf="@id/back"
                android:text="@string/any_touch_gesture"
                android:textColor="@color/white"
                android:textSize="20sp" />

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/buildInfo"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginEnd="5dp"
                android:layout_toStartOf="@id/me"
                android:text="@string/build_info"
                android:textColor="@color/white"
                android:textSize="13sp" />

            <com.google.android.material.imageview.ShapeableImageView
                android:id="@+id/me"
                android:layout_width="60dp"
                android:layout_height="60dp"
                android:layout_centerVertical="true"
                android:layout_toStartOf="@id/reward_text"
                android:src="@drawable/me"
                app:shapeAppearance="@style/RoundedImageView" />

            <com.google.android.material.textview.MaterialTextView
                android:id="@+id/reward_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginStart="10dp"
                android:layout_toStartOf="@id/reward"
                android:text="@string/reward_text"
                android:textColor="@color/white" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/reward"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginStart="10dp"
                android:text="@string/reward"
                app:icon="@drawable/ic_reward" />

        </RelativeLayout>
    </LinearLayout>

    <FrameLayout
        android:id="@+id/content"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:defaultNavHost="true"
        app:layout_constraintBottom_toTopOf="@+id/bottomNavigationView"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/app_bar_parent" />

    <io.github.netamade.ui.view.UnlimitedBottomNavigationView
        android:id="@+id/bottomNavigationView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:itemBackground="@null"
        app:labelVisibilityMode="labeled"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/content" />

</androidx.constraintlayout.widget.ConstraintLayout>