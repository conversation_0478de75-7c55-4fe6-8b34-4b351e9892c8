<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp">

    <!-- Current path display -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:orientation="horizontal">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/buttonUp"
            style="@style/Widget.Material3.Button.OutlinedButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:minWidth="48dp"
            app:icon="@drawable/ic_up" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/buttonRefresh"
            style="@style/Widget.Material3.Button.OutlinedButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:minWidth="48dp"
            app:icon="@drawable/ic_refresh" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/buttonPreset"
            style="@style/Widget.Material3.Button.OutlinedButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:minWidth="48dp"
            android:text="预设"
            android:textSize="12sp" />

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/tvTip"
            style="@style/Widget.Material3.Button.OutlinedButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:enabled="false"
            android:minWidth="48dp" />

    </LinearLayout>

    <com.google.android.material.textview.MaterialTextView
        android:id="@+id/textCurrentPath"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:background="@android:color/darker_gray"
        android:padding="8dp"
        android:text="/storage/emulated/0"
        android:textStyle="bold" />

    <!-- File list -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerView"
        android:layout_width="match_parent"
        android:layout_height="400dp" />

</LinearLayout>
