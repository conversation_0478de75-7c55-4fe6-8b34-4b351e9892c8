<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:padding="16dp">

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@android:id/icon"
        android:layout_width="?android:listPreferredItemHeight"
        android:layout_height="?android:listPreferredItemHeight"
        android:scaleType="centerCrop"
        android:src="@drawable/ic_unknown" />

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:orientation="vertical"
        android:paddingStart="15dp">

        <com.google.android.material.textview.MaterialTextView
            android:id="@android:id/title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:maxLines="1"
            android:textAppearance="?android:attr/textAppearanceMedium" />

        <com.google.android.material.textview.MaterialTextView
            android:id="@android:id/summary"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:maxLines="1"
            android:textAppearance="?android:attr/textAppearanceSmall" />
    </LinearLayout>

    <com.google.android.material.button.MaterialButtonToggleGroup
        android:id="@+id/toggleButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:checkedButton="@id/toggle_disabled"
        app:selectionRequired="true"
        app:singleSelection="true">

        <com.google.android.material.button.MaterialButton
            android:id="@+id/toggle_keeping_status"
            style="?attr/materialButtonOutlinedStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/keeping_accessibility" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/toggle_enabled"
            style="?attr/materialButtonOutlinedStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/enabled" />

        <com.google.android.material.button.MaterialButton
            android:id="@+id/toggle_disabled"
            style="?attr/materialButtonOutlinedStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/disabled" />
    </com.google.android.material.button.MaterialButtonToggleGroup>

</LinearLayout>
