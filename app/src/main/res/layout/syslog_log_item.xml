<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/log_container"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:animateLayoutChanges="true"
    android:background="?android:attr/selectableItemBackground"
    android:clickable="true"
    android:focusable="true"
    android:orientation="vertical"
    android:paddingStart="10dp"
    android:paddingTop="5dp"
    android:paddingEnd="10dp"
    android:paddingBottom="5dp">

    <!-- 第一行：时间、等级、TAG -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tv_level"
            android:layout_width="15dp"
            android:layout_height="wrap_content"
            android:background="@color/colorError"
            android:gravity="center"
            android:text="E"
            android:textColor="#ffffff"
            android:textSize="12sp" />

        <TextView
            android:id="@+id/tv_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@color/colorAccent"
            android:paddingStart="3dp"
            android:paddingEnd="3dp"
            android:text="03-02 15:21:41.869"
            android:textColor="#ffffff"
            android:textSize="12sp" />

        <TextView
            android:id="@+id/tv_pid"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="#FF794F"
            android:paddingStart="3dp"
            android:paddingEnd="3dp"
            android:text="PID: 11111 PPID: 22222"
            android:textColor="#ffffff"
            android:textSize="12sp" />

        <TextView
            android:id="@+id/tv_tag"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@color/colorPrimary"
            android:maxLines="1"
            android:paddingStart="3dp"
            android:paddingEnd="3dp"
            android:text="Neta is ok"
            android:textColor="#ffffff"
            android:textSize="12sp" />
    </LinearLayout>

    <!-- 第二行：日志内容 -->
    <TextView
        android:id="@+id/tv_message"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="2dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:text="@string/reward_text"
        android:textColor="#000000"
        android:textSize="14sp" />

</LinearLayout>