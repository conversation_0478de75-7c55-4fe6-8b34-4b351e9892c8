<!-- res/layout/dialog_reward.xml -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <LinearLayout
        android:id="@+id/tab_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="horizontal">

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/tab_wechat"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@drawable/tab_background_unselected"
            android:gravity="center"
            android:padding="16dp"
            android:text="@string/wechat" />

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/tab_alipay"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@drawable/tab_background_selected"
            android:gravity="center"
            android:padding="16dp"
            android:text="@string/alipay" />

    </LinearLayout>

    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/image_qr_code"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:scaleType="centerInside" />

    <com.google.android.material.button.MaterialButton
        android:id="@+id/button_close"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="@string/close" />
</LinearLayout>
