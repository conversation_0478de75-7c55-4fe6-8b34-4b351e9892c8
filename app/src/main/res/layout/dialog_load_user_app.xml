<!-- res/layout/dialog_reward.xml -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/loadingMessage"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="start"
            android:text="@string/loading"
            android:textStyle="bold"
            android:typeface="monospace" />

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/progressMessage"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_toStartOf="@id/loadingMessage"
            android:gravity="end"
            android:text="@string/package_name"
            android:textStyle="bold"
            android:typeface="monospace" />

    </RelativeLayout>

    <com.google.android.material.textview.MaterialTextView
        android:id="@+id/packageName"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="end"
        android:text="@string/package_name"
        android:textStyle="bold"
        android:typeface="monospace" />

    <ProgressBar
        android:id="@+id/progressBar"
        style="@style/Widget.AppCompat.ProgressBar.Horizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:indeterminate="false"
        android:max="100"
        android:progress="50" />
</LinearLayout>
