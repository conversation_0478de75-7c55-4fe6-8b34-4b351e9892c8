<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- App Name -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:hint="应用名称">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/editAppName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Package Name -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:hint="包名">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/editPackageName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Class Name -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:hint="Activity 类名">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/editClassName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Icon -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:hint="图标名称">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/editIcon"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- Switches -->
        <com.google.android.material.switchmaterial.SwitchMaterial
            android:id="@+id/switchIsUsed"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:text="启用" />

        <com.google.android.material.switchmaterial.SwitchMaterial
            android:id="@+id/switchCloudConfigFlag"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:text="云端配置标志" />

        <com.google.android.material.switchmaterial.SwitchMaterial
            android:id="@+id/switchRedPointFlag"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:text="红点标志" />

        <com.google.android.material.switchmaterial.SwitchMaterial
            android:id="@+id/switchIsNewAdd"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="新添加" />

    </LinearLayout>

</ScrollView>
