<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!-- 主内容区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <!-- 左侧配置列表 -->
        <LinearLayout
            android:id="@+id/property_list_panel"
            android:layout_width="400dp"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:padding="8dp">

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/search_input_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="搜索配置">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/search_input"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />
            </com.google.android.material.textfield.TextInputLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/property_recycler_view"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1" />
        </LinearLayout>

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/no_selection_text"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:text="未选中配置值，请在左侧选中"
            android:textAppearance="?attr/textAppearanceHeadline5"
            android:visibility="visible" />

        <!-- 右侧详情面板 -->
        <androidx.core.widget.NestedScrollView
            android:id="@+id/property_detail_panel"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:visibility="gone"
            android:layout_weight="1"
            android:padding="8dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <!-- 配置字段 -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/property_name_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="配置">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/property_name"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:enabled="false" />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/property_key_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:hint="Key">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/property_key"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:enabled="false" />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/property_id_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:hint="ID">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/property_id"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:enabled="false" />
                </com.google.android.material.textfield.TextInputLayout>

                <!-- 数据类型选择 -->
                <com.google.android.material.textview.MaterialTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:text="数据类型"
                    android:textAppearance="?attr/textAppearanceBody1" />

                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_marginTop="10dp"
                    android:layout_height="wrap_content"
                    app:cardElevation="0dp">

                    <Spinner
                        android:id="@+id/data_type_spinner"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />

                </com.google.android.material.card.MaterialCardView>

                <!-- 操作按钮 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_marginTop="10dp"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/read_button"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="读取数据" />

                    <com.google.android.material.button.MaterialButton
                        android:id="@+id/write_button"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="写入数据" />
                </LinearLayout>

                <!-- 配置值 -->
                <com.google.android.material.textfield.TextInputLayout
                    android:id="@+id/property_value_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="配置值">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/property_value"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:maxHeight="200dp"
                        android:minHeight="60dp"
                        android:textSize="14sp"
                        android:typeface="monospace" />
                </com.google.android.material.textfield.TextInputLayout>

                <!-- 响应结果 -->
                <com.google.android.material.textview.MaterialTextView
                    android:id="@+id/response_text"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textAppearance="?attr/textAppearanceBody2" />


                <com.google.android.material.button.MaterialButton
                    android:id="@+id/reset_vehicle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:text="重置保养 (测试)" />

            </LinearLayout>
        </androidx.core.widget.NestedScrollView>
    </LinearLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>