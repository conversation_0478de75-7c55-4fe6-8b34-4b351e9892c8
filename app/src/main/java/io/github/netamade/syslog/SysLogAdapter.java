package io.github.netamade.syslog;

import android.graphics.Color;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

import io.github.netamade.R;

public class SysLogAdapter extends RecyclerView.Adapter<SysLogAdapter.ViewHolder> {
    private static final int MAX_SIZE = 10000;

    private final List<LogData> logs = new ArrayList<>();
    private Filter currentFilter = log -> true;

    public void clear() {
        int size = logs.size();
        logs.clear();
        notifyItemRangeRemoved(0, size);
    }

    public interface Filter {
        boolean apply(LogData log);
    }

    public void setFilter(Filter filter) {
        this.currentFilter = filter;
        logs.removeIf(f -> !currentFilter.apply(f));
        notifyDataSetChanged();
    }

    public void append(List<LogData> addList) {
        addList = addList.stream().filter(f -> currentFilter.apply(f)).collect(Collectors.toList());

        if (addList.isEmpty()) {
            return;
        }

        int i1 = addList.size() - MAX_SIZE;
        if (i1 > 0) {
            addList = addList.subList(i1, addList.size() - 1);
        }

        int size1 = logs.size();
        int delta = size1 + addList.size() - MAX_SIZE;
        if (delta > 0) {
            if (delta >= MAX_SIZE) {
                logs.clear();
                notifyItemRangeRemoved(0, size1);
            } else {
                for (int i = 0; i < delta; i++) {
                    logs.remove(0);
                }
                notifyItemRangeRemoved(0, delta);
            }
        }
        int size = addList.size();
        logs.addAll(addList);
        notifyItemRangeInserted(size - 1, addList.size());

    }


    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.syslog_log_item, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        LogData log = logs.get(position);
        holder.bind(log);
    }

    @Override
    public int getItemCount() {
        return logs.size();
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        private final TextView tvTime;
        private final TextView tvLevel;
        private final TextView tvTag;
        private final TextView tvPid;
        private final TextView tvMessage;

        ViewHolder(View itemView) {
            super(itemView);
            tvTime = itemView.findViewById(R.id.tv_time);
            tvLevel = itemView.findViewById(R.id.tv_level);
            tvTag = itemView.findViewById(R.id.tv_tag);
            tvPid = itemView.findViewById(R.id.tv_pid);
            tvMessage = itemView.findViewById(R.id.tv_message);

            tvLevel.setTextColor(Color.WHITE);

            tvMessage.setOnClickListener(v -> {
                TextView tv = (TextView) v;
                if (tv.getMaxLines() == 1) {
                    tv.setMaxLines(Integer.MAX_VALUE);
                    tv.setEllipsize(null);
                } else {
                    tv.setMaxLines(1);
                    tv.setEllipsize(TextUtils.TruncateAt.END);
                }
            });
        }

        void bind(LogData log) {
            tvTime.setText(log.getTime());
            tvLevel.setText(log.getLevel());
            tvMessage.setText(log.getMessage());
            tvTag.setText(log.getTag());

            tvPid.setText(String.format(Locale.CHINA, "PID: %-7d PPID: %-7d", log.getPid(), log.getPpid()));

            // 根据日志级别设置背景色
            Integer color = colorByLevel.getOrDefault(log.getLevel(), 0xFF848B9C);
            tvLevel.setBackgroundColor(color);
        }

        // 或者使用更简洁的十六进制写法：
        Map<String, Integer> colorByLevel = new HashMap<>();

        {
            colorByLevel.put("V", 0xFF848B9C);  // VERBOSE
            colorByLevel.put("D", 0xFF2196F3);  // DEBUG
            colorByLevel.put("I", 0xFF4CAF50);  // INFO
            colorByLevel.put("W", 0xFFFF9800);  // WARN
            colorByLevel.put("E", 0xFFF44336);  // ERROR
            colorByLevel.put("T", 0xFF9C27B0);  // WTF
            colorByLevel.put("F", 0xFF9C27B0);  // WTF
        }
    }
}