package io.github.netamade.syslog;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class LogData {
    public static final Pattern logRegex = Pattern.compile("(\\d{2}-\\d{2}\\s+\\d{2}:\\d{2}:\\d{2}.\\d{3})\\s+(\\d+)\\s+(\\d+)\\s+([VDIWETF])\\s+([^:]+):(.+)");

    private final String time;
    private final String level;
    private final long ppid;
    private final long pid;
    private final String tag;
    private final String message;
    private final String rawMessage;

    public static LogData parse(String line) {
        Matcher matcher = logRegex.matcher(line);
        if (matcher.find()) {
            String time = trim(matcher.group(1));
            String ppid = trim(matcher.group(2));
            String pid = trim(matcher.group(3));
            String level = trim(matcher.group(4));
            String tag = trim(matcher.group(5));
            String message = trim(matcher.group(6));

            return new LogData(time, level, toLong(ppid), toLong(pid), tag, message, line);
        } else {
            return null;
        }
    }

    private static String trim(String s) {
        if (s != null) {
            return s.trim();
        }
        return null;
    }

    private static long toLong(String pid) {
        try {
            return Long.parseLong(pid.trim());
        } catch (Throwable ignored) {
            return 0L;
        }
    }

}
