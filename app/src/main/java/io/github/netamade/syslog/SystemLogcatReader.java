package io.github.netamade.syslog;

import android.app.HozonPermissionManager;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

public class SystemLogcatReader extends Thread {

    public static final String TAG = SystemLogcatReader.class.getSimpleName();
    private final List<SysLogCallback> callbacks = new ArrayList<>();
    private Process process = null;

    private final AtomicBoolean isRunning = new AtomicBoolean();


    private static volatile SystemLogcatReader instance = null;

    public static SystemLogcatReader getOrCreate() {
        if (instance == null || !instance.isRunning()) {
            synchronized (SystemLogcatReader.class) {
                if (instance == null || !instance.isRunning()) {
                    instance = new SystemLogcatReader();
                    instance.start();
                }
            }
        }
        return instance;
    }

    private SystemLogcatReader(SysLogCallback... callbacks) {
        super(TAG);
        if (callbacks != null) {
            this.callbacks.addAll(Arrays.asList(callbacks));
        }
    }

    public void addCallback(SysLogCallback callback) {
        synchronized (this.callbacks) {
            if (this.callbacks.contains(callback)) {
                return;
            }
            this.callbacks.add(callback);
        }
    }

    public void removeCallback(SysLogCallback callback) {
        synchronized (this.callbacks) {
            this.callbacks.remove(callback);
        }
    }

    public void exit() {
        isRunning.set(false);
    }

    public boolean isRunning() {
        return process != null && process.isAlive();
    }

    @Override
    public void run() {
        super.run();
        isRunning.set(true);
        try {
            ProcessBuilder processBuilder = new ProcessBuilder("logcat");
            process = processBuilder.start();

            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;

            while (isRunning.get() && (line = reader.readLine()) != null) {

                LogData logData = LogData.parse(line);
                if (logData == null) {
                    continue;
                }

                if (!this.callbacks.isEmpty()) {
                    this.callbacks.forEach(fn -> {
                        fn.onLogData(logData);
                    });
                }
            }
            reader.close();
            process.destroy();
            process = null;
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            isRunning.set(false);
        }
    }

}
