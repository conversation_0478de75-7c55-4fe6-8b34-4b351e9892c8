package io.github.netamade.ui.dialog;

import android.content.Context;
import android.util.Log;
import android.view.LayoutInflater;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AlertDialog;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.google.android.material.dialog.MaterialAlertDialogBuilder;
import com.topjohnwu.superuser.Shell;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

import io.github.netamade.AppSettings;
import io.github.netamade.adapter.FilePickerAdapter;
import io.github.netamade.adapter.QuickPathAdapter;
import io.github.netamade.databinding.DialogFilePickerBinding;
import io.github.netamade.entity.FileItem;
import io.github.netamade.util.ShellUtils;
import lombok.Setter;

public class FilePickerDialog {
    private final Context context;
    private DialogFilePickerBinding binding;
    private AlertDialog dialog;
    private FilePickerAdapter adapter;
    private List<FileItem> fileItems;
    private File currentDirectory;
    @Setter
    private String fileExtensionFilter;
    @Setter
    private OnFileSelectedListener onFileSelectedListener;
    private List<QuickPath> quickPaths;
    private final java.util.concurrent.ExecutorService executorService = java.util.concurrent.Executors.newSingleThreadExecutor();
    private final android.os.Handler mainHandler = new android.os.Handler(android.os.Looper.getMainLooper());

    public interface OnFileSelectedListener {
        void onFileSelected(File file);
    }

    public static class QuickPath {
        public final String name;
        public final String path;

        public QuickPath(String name, String path) {
            this.name = name;
            this.path = path;
        }
    }

    public FilePickerDialog(@NonNull Context context) {
        this.context = context;
        this.fileItems = new ArrayList<>();
        this.quickPaths = new ArrayList<>();
        this.currentDirectory = new File(AppSettings.getCurrentDirectory());
        initDialog();
    }

    private void initDialog() {
        binding = DialogFilePickerBinding.inflate(LayoutInflater.from(context));

        adapter = new FilePickerAdapter(context, fileItems);
        adapter.setOnItemClickListener(this::onItemClick);

        binding.recyclerView.setLayoutManager(new LinearLayoutManager(context));
        binding.recyclerView.setAdapter(adapter);

        binding.buttonUp.setOnClickListener(v -> navigateUp());
        binding.buttonRefresh.setOnClickListener(v -> refreshCurrentDirectory());
        binding.buttonPreset.setOnClickListener(v -> showPresetDialog());

    }

    public void addQuickPath(String name, String path) {
        quickPaths.add(new QuickPath(name, path));
    }

    public void show() {
        MaterialAlertDialogBuilder builder = new MaterialAlertDialogBuilder(context);
        builder.setTitle("选择文件")
                .setView(binding.getRoot())
                .setNegativeButton("取消", null);

        dialog = builder.create();
        dialog.show();

        // 检查 root 权限并显示状态
        checkRootAccessAndRefresh();
    }

    private void checkRootAccessAndRefresh() {
        executorService.submit(() -> {
            boolean hasRoot = hasRootAccess();
            mainHandler.post(() -> {
                if (!hasRoot) {
                    Toast.makeText(context, "未获取 Root 权限，部分目录可能无法访问", Toast.LENGTH_SHORT).show();
                }
                refreshCurrentDirectory();
            });
        });
    }

    private void onItemClick(FileItem item) {
        File file = new File(item.getPath());

        if (item.isDirectory()) {
            currentDirectory = file;
            refreshCurrentDirectory();
        } else {
            if (onFileSelectedListener != null) {
                onFileSelectedListener.onFileSelected(file);
            }
            dialog.dismiss();
        }
    }

    private void navigateUp() {
        File parent = currentDirectory.getParentFile();
        if (parent != null) {
            currentDirectory = parent;
            refreshCurrentDirectory();
        }
    }

    private void refreshCurrentDirectory() {
        binding.textCurrentPath.setText(currentDirectory.getAbsolutePath());
        if (fileExtensionFilter != null && !fileExtensionFilter.isBlank()) {
            binding.tvTip.setText("仅显示: " + fileExtensionFilter + " 后缀");
        } else {
            binding.tvTip.setText("");
        }
        AppSettings.setCurrentDirectory(currentDirectory.getAbsolutePath());

        // Show loading state
        fileItems.clear();
        adapter.notifyDataSetChanged();

        // Use background thread for root operations
        executorService.submit(() -> {
            try {
                List<FileItem> newItems = listDirectoryWithRoot(currentDirectory.getAbsolutePath());

                // Update UI on main thread
                mainHandler.post(() -> {
                    fileItems.clear();
                    fileItems.addAll(newItems);
                    adapter.notifyDataSetChanged();
                });

            } catch (Exception e) {
                mainHandler.post(() -> {
                    Toast.makeText(context, "无法访问此目录: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                    // Fallback to normal file listing
                    refreshCurrentDirectoryNormal();
                });
            }
        });
    }

    private void refreshCurrentDirectoryNormal() {
        fileItems.clear();

        try {
            File[] files = currentDirectory.listFiles();
            if (files != null) {
                java.util.Arrays.sort(files, (f1, f2) -> {
                    if (f1.isDirectory() && !f2.isDirectory()) return -1;
                    if (!f1.isDirectory() && f2.isDirectory()) return 1;
                    return f1.getName().compareToIgnoreCase(f2.getName());
                });

                for (File file : files) {
                    if (file.isDirectory()) {
                        fileItems.add(new FileItem(file.getName(), file.getAbsolutePath(), "", true));
                    } else if (fileExtensionFilter == null || file.getName().endsWith(fileExtensionFilter)) {
                        fileItems.add(new FileItem(file.getName(), file.getAbsolutePath(), "", false));
                    }
                }
            }
        } catch (SecurityException e) {
            Toast.makeText(context, "无法访问此目录: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        }

        adapter.notifyDataSetChanged();
    }

    public void navigateToPath(String path) {
        File file = new File(path);
        currentDirectory = file;
        refreshCurrentDirectory();
    }

    /**
     * 使用 root 权限列出目录内容
     */
    private List<FileItem> listDirectoryWithRoot(String directoryPath) throws Exception {
        List<FileItem> items = new ArrayList<>();

        Shell.Result result = Shell.cmd(String.format("ls -la '%s'", directoryPath)).exec();

        for (String line : result.getOut()) {
            Log.i("Root", line);
            FileItem item = parseListOutput(line, directoryPath);
            if (item != null) {
                // 应用文件过滤器
                if (item.isDirectory() || fileExtensionFilter == null ||
                        item.getName().endsWith(fileExtensionFilter)) {
                    items.add(item);
                }
            }
        }

        // 排序：目录在前，文件在后，按名称排序
        items.sort((f1, f2) -> {
            if (f1.isDirectory() && !f2.isDirectory()) return -1;
            if (!f1.isDirectory() && f2.isDirectory()) return 1;
            return f1.getName().compareToIgnoreCase(f2.getName());
        });
        return items;
    }

    /**
     * 解析 ls -la 命令的输出行
     */
    private FileItem parseListOutput(String line, String parentPath) {
        if (line == null || line.trim().isEmpty()) {
            return null;
        }

        // 跳过总计行和特殊目录
        if (line.startsWith("total") || line.trim().equals(".") || line.trim().equals("..")) {
            return null;
        }

        // ls -la 输出格式: drwxrwxrwx 1 <USER> <GROUP> size date time filename
        String[] parts = line.trim().split("\\s+");
        if (parts.length < 8) {
            return null;
        }

        // 获取权限字符串
        String permissions = parts[0];
        boolean isDirectory = permissions.startsWith("d");

        // 获取文件名（可能包含空格，所以需要重新组合）
        StringBuilder nameBuilder = new StringBuilder();
        for (int i = 7; i < parts.length; i++) {
            if (i > 7) nameBuilder.append(" ");
            nameBuilder.append(parts[i]);
        }
        String fileName = nameBuilder.toString();

        if (fileName.trim().equals(".") || fileName.trim().equals("..")) {
            return null;
        }
        // 跳过隐藏文件（以.开头的文件，除了当前目录和父目录）
        if (fileName.startsWith(".") && !fileName.equals(".") && !fileName.equals("..")) {
            return null;
        }

        // 构建完整路径
        String fullPath = parentPath.endsWith("/") ? parentPath + fileName : parentPath + "/" + fileName;

        return new FileItem(fileName, fullPath, permissions, isDirectory);
    }

    /**
     * 检查是否有 root 权限
     */
    private boolean hasRootAccess() {
        try {
            Process process = Runtime.getRuntime().exec("su -c id");
            java.io.BufferedReader reader = new java.io.BufferedReader(
                    new java.io.InputStreamReader(process.getInputStream()));
            String result = reader.readLine();
            reader.close();

            int exitCode = process.waitFor();
            return exitCode == 0 && result != null && result.contains("uid=0");
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 显示预设路径选择对话框
     */
    private void showPresetDialog() {
        if (quickPaths.isEmpty()) {
            Toast.makeText(context, "没有可用的预设路径", Toast.LENGTH_SHORT).show();
            return;
        }

        // 创建RecyclerView布局
        androidx.recyclerview.widget.RecyclerView recyclerView = new androidx.recyclerview.widget.RecyclerView(context);
        recyclerView.setLayoutManager(new LinearLayoutManager(context));
        recyclerView.setPadding(16, 16, 16, 16);

        // 创建对话框
        AlertDialog presetDialog = new MaterialAlertDialogBuilder(context)
                .setTitle("选择预设路径")
                .setView(recyclerView)
                .setNegativeButton("取消", null)
                .create();

        // 创建适配器
        QuickPathAdapter adapter = new QuickPathAdapter(context, quickPaths);
        adapter.setOnItemClickListener(quickPath -> {
            navigateToPath(quickPath.path);
            presetDialog.dismiss(); // 关闭预设对话框
        });
        recyclerView.setAdapter(adapter);

        presetDialog.show();
    }
}
