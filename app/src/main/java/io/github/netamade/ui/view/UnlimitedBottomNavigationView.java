package io.github.netamade.ui.view;

import android.content.Context;
import android.util.AttributeSet;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.google.android.material.bottomnavigation.BottomNavigationView;

public class UnlimitedBottomNavigationView extends BottomNavigationView {
    public UnlimitedBottomNavigationView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
    }

    @Override
    public int getMaxItemCount() {
        return 20;
    }
}
