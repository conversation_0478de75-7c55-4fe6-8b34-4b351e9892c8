package io.github.netamade.ui.fragment;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.ItemTouchHelper;

import com.google.android.material.dialog.MaterialAlertDialogBuilder;

import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import io.github.netamade.AppContext;
import io.github.netamade.R;
import io.github.netamade.adapter.AppIconGridAdapter;
import io.github.netamade.databinding.LayoutAppIconEditorBinding;
import io.github.netamade.entity.AppIconItem;
import io.github.netamade.entity.LayoutData;
import io.github.netamade.ui.dialog.AppIconEditDialog;
import io.github.netamade.ui.dialog.FilePickerDialog;
import io.github.netamade.ui.dialog.MultiUserAppPickerDialog;
import io.github.netamade.util.SharedPrefsXmlParser;

public class AppIconEditorFragment extends BaseFragment {

    public AppIconEditorFragment() {
        super(AppContext.applicationContext().getString(R.string.app_icon_editor));
    }

    private LayoutAppIconEditorBinding binding;
    private AppIconGridAdapter adapter;
    private List<AppIconItem> appIconItems;
    private LayoutData currentLayoutData;
    private File currentXmlFile;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = LayoutAppIconEditorBinding.inflate(inflater, container, false);

        initViews();
        setupRecyclerView();
        setupClickListeners();
        checkRootPermission();

        return binding.getRoot();
    }

    private void initViews() {
        appIconItems = new ArrayList<>();
        adapter = new AppIconGridAdapter(getContext(), appIconItems);
        adapter.setOnItemClickListener(this::onItemClick);
        adapter.setOnItemLongClickListener(this::onItemLongClick);
    }

    private void setupRecyclerView() {
        GridLayoutManager layoutManager = new GridLayoutManager(getContext(), 8);
        binding.recyclerView.setLayoutManager(layoutManager);
        binding.recyclerView.setAdapter(adapter);

        // Setup drag and drop
        ItemTouchHelper itemTouchHelper = new ItemTouchHelper(new ItemTouchHelper.SimpleCallback(
                ItemTouchHelper.UP | ItemTouchHelper.DOWN | ItemTouchHelper.LEFT | ItemTouchHelper.RIGHT, 0) {
            @Override
            public boolean onMove(@NonNull androidx.recyclerview.widget.RecyclerView recyclerView,
                                  @NonNull androidx.recyclerview.widget.RecyclerView.ViewHolder viewHolder,
                                  @NonNull androidx.recyclerview.widget.RecyclerView.ViewHolder target) {
                int fromPosition = viewHolder.getAdapterPosition();
                int toPosition = target.getAdapterPosition();

                AppIconItem item = appIconItems.remove(fromPosition);
                appIconItems.add(toPosition, item);
                adapter.notifyItemMoved(fromPosition, toPosition);
                // 排序， isDock 的排在最前面
                Collections.sort(appIconItems, (o1, o2) -> {
                    if (o1.isDock() && !o2.isDock()) {
                        return -1;
                    }
                    if (!o1.isDock() && o2.isDock()) {
                        return 1;
                    }
                    return 0;
                });
                adapter.notifyDataSetChanged();
                return true;
            }

            @Override
            public void onSwiped(@NonNull androidx.recyclerview.widget.RecyclerView.ViewHolder viewHolder, int direction) {
                // Not used
            }
        });
        itemTouchHelper.attachToRecyclerView(binding.recyclerView);
    }

    private void setupClickListeners() {
        binding.loadLayoutButton.setOnClickListener(v -> loadLayoutFile());
        binding.saveLayoutButton.setOnClickListener(v -> saveLayoutFile());
        binding.fabAddApp.setOnClickListener(v -> addNewApp());

        // New buttons for restart and reset functionality
        binding.restartMainLauncherButton.setOnClickListener(v -> restartMainLauncher());
        binding.restartViceLauncherButton.setOnClickListener(v -> restartViceLauncher());
        binding.resetMainLayoutButton.setOnClickListener(v -> resetMainLayout());
        binding.resetViceLayoutButton.setOnClickListener(v -> resetViceLayout());
    }

    private void loadLayoutFile() {
        FilePickerDialog dialog = new FilePickerDialog(getContext());
        dialog.setFileExtensionFilter(".xml");
        dialog.addQuickPath("L 主屏", "/data/user_de/0/com.hozonauto.netahome/shared_prefs/");
        dialog.addQuickPath("L 副屏", "/data/user_de/10/com.android.vicelauncher/shared_prefs/");
        dialog.addQuickPath("临时 Data", "/data/local/tmp/");
        dialog.setOnFileSelectedListener(this::onFileSelected);
        dialog.show();
    }

    private void onFileSelected(File file) {
        try {
            currentXmlFile = file;
            currentLayoutData = SharedPrefsXmlParser.parseXmlFile(file);
            loadAppIconsFromLayoutData();
            Toast.makeText(getContext(), "布局文件加载成功", Toast.LENGTH_SHORT).show();
        } catch (Exception e) {
            e.printStackTrace();
            Toast.makeText(getContext(), "加载布局文件失败: " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }

    private void loadAppIconsFromLayoutData() {
        appIconItems.clear();
        if (currentLayoutData != null) {
            if (currentLayoutData.getAppListData() != null) {
                appIconItems.addAll(currentLayoutData.getAppListData());
            }
            if (currentLayoutData.getDockData() != null) {
                appIconItems.addAll(currentLayoutData.getDockData());
            }
        }
        // 排序， isDock 的排在最前面
        Collections.sort(appIconItems, (o1, o2) -> {
            if (o1.isDock() && !o2.isDock()) {
                return -1;
            }
            if (!o1.isDock() && o2.isDock()) {
                return 1;
            }
            return 0;
        });
        adapter.notifyDataSetChanged();
    }

    private void addNewApp() {
        MultiUserAppPickerDialog dialog = new MultiUserAppPickerDialog(getContext());
        dialog.setOnAppSelectedListener(this::onAppSelected);
        dialog.show();
    }

    private void onAppSelected(AppIconItem appIconItem) {
        appIconItems.add(appIconItem);
        adapter.notifyItemInserted(appIconItems.size() - 1);
    }

    private void onItemClick(int position) {
        // Show edit dialog for the item
        AppIconItem item = appIconItems.get(position);
        showEditDialog(item, position);
    }

    private void onItemLongClick(int position) {
    }

    private void showEditDialog(AppIconItem item, int position) {
        AppIconEditDialog dialog = new AppIconEditDialog(getActivity(), item);
        dialog.setOnDeleteListener(() -> {
            if (item.getPackageName() == null || item.getPackageName().isBlank()) {
                Toast.makeText(getActivity(), "不懂别乱删", Toast.LENGTH_SHORT).show();
                return;
            }
            appIconItems.remove(position);
            adapter.notifyItemRemoved(position);
        });
        dialog.setOnSaveListener(newItem -> {
            adapter.notifyItemChanged(position);
        });

        dialog.show();
    }

    private void saveLayoutFile() {
        if (currentXmlFile == null || currentLayoutData == null) {
            Toast.makeText(getContext(), "请先加载布局文件", Toast.LENGTH_SHORT).show();
            return;
        }

        try {
            // Update layout data with current items
            updateLayoutDataFromItems();

            // Save to temporary file first, then copy to original location
            SharedPrefsXmlParser.saveXmlFile(currentXmlFile, currentLayoutData);
            Toast.makeText(getContext(), "布局文件保存成功", Toast.LENGTH_SHORT).show();
        } catch (Exception e) {
            e.printStackTrace();
            Toast.makeText(getContext(), "保存布局文件失败: " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }

    private void updateLayoutDataFromItems() {
        if (currentLayoutData == null) {
            return;
        }
        List<AppIconItem> apps = new ArrayList<>();
        List<AppIconItem> docks = new ArrayList<>();
        for (AppIconItem it : appIconItems) {
            if (it.isDock()) {
                docks.add(it);
            } else {
                apps.add(it);
            }
        }
        currentLayoutData.setAppListData(apps);
        currentLayoutData.setDockData(docks);
    }

    /**
     * 检查root权限并显示相应提示
     */
    private void checkRootPermission() {
        // 在后台线程检查root权限
        new Thread(() -> {
            boolean hasRoot = SharedPrefsXmlParser.hasRootAccess();

            // 在主线程更新UI
            if (getActivity() != null) {
                getActivity().runOnUiThread(() -> {
                    if (!hasRoot) {
                        showRootPermissionWarning();
                        disableRootRequiredButtons();
                    } else {
                        enableRootRequiredButtons();
                    }
                });
            }
        }).start();
    }

    /**
     * 显示root权限警告
     */
    private void showRootPermissionWarning() {
        new MaterialAlertDialogBuilder(getContext())
                .setTitle("权限提示")
                .setMessage("检测到设备没有Root权限，重启和重置功能将无法使用。\n\n如需使用这些功能，请确保设备已获取Root权限。")
                .setPositiveButton("我知道了", null)
                .setIcon(android.R.drawable.ic_dialog_alert)
                .show();
    }

    /**
     * 禁用需要root权限的按钮
     */
    private void disableRootRequiredButtons() {
        binding.restartMainLauncherButton.setEnabled(false);
        binding.restartViceLauncherButton.setEnabled(false);
        binding.resetMainLayoutButton.setEnabled(false);
        binding.resetViceLayoutButton.setEnabled(false);

        // 设置按钮文本提示
        binding.restartMainLauncherButton.setText("重启主屏桌面\n(需要Root)");
        binding.restartViceLauncherButton.setText("重启副屏桌面\n(需要Root)");
        binding.resetMainLayoutButton.setText("重置主屏布局\n(需要Root)");
        binding.resetViceLayoutButton.setText("重置副屏布局\n(需要Root)");
    }

    /**
     * 启用需要root权限的按钮
     */
    private void enableRootRequiredButtons() {
        binding.restartMainLauncherButton.setEnabled(true);
        binding.restartViceLauncherButton.setEnabled(true);
        binding.resetMainLayoutButton.setEnabled(true);
        binding.resetViceLayoutButton.setEnabled(true);

        // 恢复原始按钮文本
        binding.restartMainLauncherButton.setText("重启主屏桌面");
        binding.restartViceLauncherButton.setText("重启副屏桌面");
        binding.resetMainLayoutButton.setText("重置主屏布局");
        binding.resetViceLayoutButton.setText("重置副屏布局");
    }

    /**
     * 重启主屏桌面
     */
    private void restartMainLauncher() {
        try {
            SharedPrefsXmlParser.restartApp("com.hozonauto.netahome");
            Toast.makeText(getContext(), "主屏桌面重启成功", Toast.LENGTH_SHORT).show();
        } catch (Exception e) {
            e.printStackTrace();
            Toast.makeText(getContext(), "重启主屏桌面失败: " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }

    /**
     * 重启副屏桌面
     */
    private void restartViceLauncher() {
        try {
            SharedPrefsXmlParser.restartApp("com.android.vicelauncher");
            Toast.makeText(getContext(), "副屏桌面重启成功", Toast.LENGTH_SHORT).show();
        } catch (Exception e) {
            e.printStackTrace();
            Toast.makeText(getContext(), "重启副屏桌面失败: " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }

    /**
     * 重置主屏桌面布局
     */
    private void resetMainLayout() {
        new MaterialAlertDialogBuilder(getContext())
                .setTitle("确认重置")
                .setMessage("确定要重置主屏桌面布局吗？此操作将清除所有应用布局数据并重启桌面。")
                .setPositiveButton("确定", (dialog, which) -> {
                    try {
                        SharedPrefsXmlParser.resetMainLauncherLayout();
                        Toast.makeText(getContext(), "主屏桌面布局重置成功", Toast.LENGTH_SHORT).show();
                    } catch (Exception e) {
                        e.printStackTrace();
                        Toast.makeText(getContext(), "重置主屏桌面布局失败: " + e.getMessage(), Toast.LENGTH_LONG).show();
                    }
                })
                .setNegativeButton("取消", null)
                .show();
    }

    /**
     * 重置副屏桌面布局
     */
    private void resetViceLayout() {
        new MaterialAlertDialogBuilder(getContext())
                .setTitle("确认重置")
                .setMessage("确定要重置副屏桌面布局吗？此操作将清除所有应用布局数据并重启桌面。")
                .setPositiveButton("确定", (dialog, which) -> {
                    try {
                        SharedPrefsXmlParser.resetViceLauncherLayout();
                        Toast.makeText(getContext(), "副屏桌面布局重置成功", Toast.LENGTH_SHORT).show();
                    } catch (Exception e) {
                        e.printStackTrace();
                        Toast.makeText(getContext(), "重置副屏桌面布局失败: " + e.getMessage(), Toast.LENGTH_LONG).show();
                    }
                })
                .setNegativeButton("取消", null)
                .show();
    }
}
