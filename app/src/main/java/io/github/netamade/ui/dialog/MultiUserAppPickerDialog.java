package io.github.netamade.ui.dialog;

import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.os.Handler;
import android.os.Looper;
import android.view.LayoutInflater;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AlertDialog;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.google.android.material.dialog.MaterialAlertDialogBuilder;
import com.topjohnwu.superuser.Shell;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import io.github.netamade.R;
import io.github.netamade.adapter.AppPickerAdapter;
import io.github.netamade.databinding.DialogMultiUserAppPickerBinding;
import io.github.netamade.entity.AppIconItem;
import io.github.netamade.entity.AppInfo;
import io.github.netamade.entity.AppShortcut;
import io.github.netamade.util.ApkUtils;

public class MultiUserAppPickerDialog {
    private final Context context;
    private DialogMultiUserAppPickerBinding binding;
    private AlertDialog dialog;
    private AppPickerAdapter adapter;
    private List<AppInfo> appInfos;
    private OnAppSelectedListener onAppSelectedListener;
    private final ExecutorService executorService = Executors.newSingleThreadExecutor();
    private final Handler mainHandler = new Handler(Looper.getMainLooper());

    public interface OnAppSelectedListener {
        void onAppSelected(AppIconItem appIconItem);
    }

    public MultiUserAppPickerDialog(@NonNull Context context) {
        this.context = context;
        this.appInfos = new ArrayList<>();
        initDialog();
    }

    private void initDialog() {
        binding = DialogMultiUserAppPickerBinding.inflate(LayoutInflater.from(context));

        adapter = new AppPickerAdapter(context, appInfos);
        adapter.setOnItemClickListener(this::onAppClick);

        binding.recyclerView.setLayoutManager(new LinearLayoutManager(context));
        binding.recyclerView.setAdapter(adapter);

        binding.buttonLoadMainScreen.setOnClickListener(v -> loadMainScreenApps());
        binding.buttonLoadViceScreen.setOnClickListener(v -> loadViceScreenApps());
    }

    public void setOnAppSelectedListener(OnAppSelectedListener listener) {
        this.onAppSelectedListener = listener;
    }

    public void show() {
        MaterialAlertDialogBuilder builder = new MaterialAlertDialogBuilder(context);
        builder.setTitle("选择应用")
                .setView(binding.getRoot())
                .setNegativeButton("取消", null);

        dialog = builder.create();
        dialog.show();

        // Load main screen apps by default
        loadMainScreenApps();
    }

    private void onAppClick(AppInfo appInfo) {
        // Convert AppInfo to AppIconItem
        AppIconItem appIconItem = new AppIconItem();
        appIconItem.setAppName(appInfo.getAppName());
        appIconItem.setPackageName(appInfo.getPackageName());

        // Try to get the main activity
        if (appInfo.hasShortcuts() && !appInfo.getShortcuts().isEmpty()) {
            appIconItem.setClassName(appInfo.getShortcuts().get(0).getActivityClass());
        }

        appIconItem.setUsed(true);
        appIconItem.setCloudConfigFlag(true);
        appIconItem.setRedPointFlag(false);
        appIconItem.setNewAdd(true);
        appIconItem.setIconDrawable(appInfo.getIcon());
        appIconItem.setSystemApp(appInfo.isSystem());

        if (onAppSelectedListener != null) {
            onAppSelectedListener.onAppSelected(appIconItem);
        }
        dialog.dismiss();
    }

    private void loadMainScreenApps() {
        binding.buttonLoadMainScreen.setEnabled(false);
        binding.buttonLoadViceScreen.setEnabled(false);
        binding.progressBar.setVisibility(android.view.View.VISIBLE);

        executorService.submit(() -> {
            try {
                List<AppInfo> apps = loadAppsForUser(0);
                mainHandler.post(() -> {
                    appInfos.clear();
                    appInfos.addAll(apps);
                    adapter.notifyDataSetChanged();
                    binding.progressBar.setVisibility(android.view.View.GONE);
                    binding.buttonLoadMainScreen.setEnabled(true);
                    binding.buttonLoadViceScreen.setEnabled(true);
                });
            } catch (Exception e) {
                mainHandler.post(() -> {
                    Toast.makeText(context, "加载主屏应用失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                    binding.progressBar.setVisibility(android.view.View.GONE);
                    binding.buttonLoadMainScreen.setEnabled(true);
                    binding.buttonLoadViceScreen.setEnabled(true);
                });
            }
        });
    }

    private void loadViceScreenApps() {
        binding.buttonLoadMainScreen.setEnabled(false);
        binding.buttonLoadViceScreen.setEnabled(false);
        binding.progressBar.setVisibility(android.view.View.VISIBLE);

        executorService.submit(() -> {
            try {
                List<AppInfo> apps = loadAppsForUser(10);
                mainHandler.post(() -> {
                    appInfos.clear();
                    appInfos.addAll(apps);
                    adapter.notifyDataSetChanged();
                    binding.progressBar.setVisibility(android.view.View.GONE);
                    binding.buttonLoadMainScreen.setEnabled(true);
                    binding.buttonLoadViceScreen.setEnabled(true);
                });
            } catch (Exception e) {
                mainHandler.post(() -> {
                    Toast.makeText(context, "加载副屏应用失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                    binding.progressBar.setVisibility(android.view.View.GONE);
                    binding.buttonLoadMainScreen.setEnabled(true);
                    binding.buttonLoadViceScreen.setEnabled(true);
                });
            }
        });
    }

    private List<AppInfo> loadAppsForUser(int userId) {
        List<AppInfo> apps = new ArrayList<>();

        if (userId == 0) {
            // Load main screen apps using PackageManager
            PackageManager pm = context.getPackageManager();
            List<PackageInfo> installedPackages = pm.getInstalledPackages(
                    PackageManager.GET_ACTIVITIES | PackageManager.GET_SERVICES);

            for (PackageInfo packageInfo : installedPackages) {
                AppInfo appInfo = ApkUtils.parseApk(pm, packageInfo);
                apps.add(appInfo);
            }
        } else {
            // 使用 pm 命令获取指定用户 id 10 的应用列表和 activity 信息
            apps = loadAppsForUserWithShell(userId);
        }

        // Sort: user apps first, then system apps
        apps.sort((o1, o2) -> {
            if (!o1.isSystem() && o2.isSystem()) return -1;
            if (o1.isSystem() && !o2.isSystem()) return 1;
            return o1.getAppName().compareToIgnoreCase(o2.getAppName());
        });

        return apps;
    }

    /**
     * 使用Shell命令获取指定用户的应用列表
     */
    private List<AppInfo> loadAppsForUserWithShell(int userId) {
        List<AppInfo> apps = new ArrayList<>();

        try {
            // 获取指定用户的所有包名
            Shell.Result result = Shell.cmd("pm list packages --user " + userId).exec();

            if (!result.isSuccess()) {
                throw new RuntimeException("Failed to get package list for user " + userId);
            }

            List<String> packageLines = result.getOut();
            for (String line : packageLines) {
                if (line.startsWith("package:")) {
                    String packageName = line.substring(8); // 移除 "package:" 前缀

                    try {
                        AppInfo appInfo = loadAppInfoWithShell(packageName, userId);
                        if (appInfo != null) {
                            apps.add(appInfo);
                        }
                    } catch (Exception e) {
                        // 跳过无法解析的应用
                        android.util.Log.w("MultiUserAppPicker", "Failed to load app info for " + packageName, e);
                    }
                }
            }

        } catch (Exception e) {
            android.util.Log.e("MultiUserAppPicker", "Failed to load apps for user " + userId, e);
            throw new RuntimeException("Failed to load apps for user " + userId + ": " + e.getMessage());
        }

        return apps;
    }

    /**
     * 使用Shell命令获取指定包名和用户的应用信息
     */
    private AppInfo loadAppInfoWithShell(String packageName, int userId) {
        try {
            // 获取应用基本信息
            Shell.Result appInfoResult = Shell.cmd("dumpsys package " + packageName + " | grep -E '(versionName|applicationLabel|system)'").exec();

            if (!appInfoResult.isSuccess()) {
                return null;
            }

            AppInfo appInfo = new AppInfo();
            appInfo.setPackageName(packageName);

            // 解析应用信息
            String appName = packageName; // 默认使用包名
            boolean isSystem = false;

            for (String line : appInfoResult.getOut()) {
                line = line.trim();
                if (line.contains("applicationLabel")) {
                    // 提取应用名称
                    int startIndex = line.indexOf("=");
                    if (startIndex != -1 && startIndex + 1 < line.length()) {
                        appName = line.substring(startIndex + 1).trim();
                    }
                } else if (line.contains("system")) {
                    isSystem = true;
                }
            }

            appInfo.setAppName(appName);
            appInfo.setSystem(isSystem);

            // 获取主Activity
            Shell.Result activityResult = Shell.cmd("pm resolve-activity --brief --user " + userId + " -c android.intent.category.LAUNCHER " + packageName).exec();

            if (activityResult.isSuccess() && !activityResult.getOut().isEmpty()) {
                for (String activityLine : activityResult.getOut()) {
                    if (activityLine.contains("/")) {
                        String[] parts = activityLine.split("/");
                        if (parts.length >= 2) {
                            String activityName = parts[1];
                            if (activityName.startsWith(".")) {
                                activityName = packageName + activityName;
                            }

                            // 创建快捷方式信息
                            AppShortcut shortcut = new AppShortcut(appName, packageName, activityName);

                            List<AppShortcut> shortcuts = new ArrayList<>();
                            shortcuts.add(shortcut);
                            appInfo.setShortcuts(shortcuts);
                            break;
                        }
                    }
                }
            }

            // 尝试获取应用图标（这里可能需要更复杂的逻辑）
            try {
                PackageManager pm = context.getPackageManager();
                appInfo.setIcon(pm.getApplicationIcon(packageName));
            } catch (Exception e) {
                // 使用默认图标
                appInfo.setIcon(context.getDrawable(android.R.drawable.sym_def_app_icon));
            }

            return appInfo;

        } catch (Exception e) {
            android.util.Log.e("MultiUserAppPicker", "Failed to load app info for " + packageName, e);
            return null;
        }
    }
}
