package io.github.netamade.ui.fragment;

import android.Manifest;
import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.preference.Preference;
import androidx.preference.SwitchPreferenceCompat;

import io.github.netamade.AppContext;
import io.github.netamade.R;
import io.github.netamade.exemptions.Exemptions;
import io.github.netamade.ui.preference.IndicatedSeekBarPreference;
import io.github.netamade.util.HozonSettings;
import io.github.netamade.util.PermissionUtils;

public class AppDebugFragment extends BasePreferenceFragment {
    public AppDebugFragment() {
        super(AppContext.applicationContext().getString(R.string.app_debug));
    }

    private Preference refreshData;
    private SwitchPreferenceCompat systemSettingsAvailability;
    private SwitchPreferenceCompat exemptionsAvailability;
    private SwitchPreferenceCompat permissionManagerAvailability;
    private SwitchPreferenceCompat carLocationChecked;
    private IndicatedSeekBarPreference carLocationAuthorizedDays;
    private SwitchPreferenceCompat carAccessFineLocationState;
    private SwitchPreferenceCompat carAccessCoarseLocationState;


    @Override
    public void onCreatePreferences(@Nullable Bundle savedInstanceState, @Nullable String rootKey) {
        addPreferencesFromResource(R.xml.app_debug);

        refreshData = findPreference("refresh_data");
        systemSettingsAvailability = findPreference("system_settings_availability");
        exemptionsAvailability = findPreference("exemptions_availability");
        permissionManagerAvailability = findPreference("permission_manager_availability");
        carLocationChecked = findPreference("car_location_checked");
        carLocationAuthorizedDays = findPreference("car_location_authorized_days");
        carAccessFineLocationState = findPreference("car_access_fine_location_state");
        carAccessCoarseLocationState = findPreference("car_access_coarse_location_state");
        refreshData();
        initListeners();
    }

    private void initListeners() {
        refreshData.setOnPreferenceClickListener(new Preference.OnPreferenceClickListener() {
            @Override
            public boolean onPreferenceClick(@NonNull Preference preference) {
                refreshData();
                return true;
            }
        });

        systemSettingsAvailability.setOnPreferenceChangeListener((preference, newValue) -> false);
        exemptionsAvailability.setOnPreferenceChangeListener((preference, newValue) -> false);
        permissionManagerAvailability.setOnPreferenceChangeListener((preference, newValue) -> false);

        carLocationChecked.setOnPreferenceChangeListener(((preference, newValue) -> {

            boolean checked = (boolean) newValue;
            boolean ok = HozonSettings.setLocationSwitchChecked(checked);

            return ok && HozonSettings.isLocationSwitchChecked() == checked;

        }));

        carLocationAuthorizedDays.setOnPreferenceChangeListener(((preference, newValue) -> {
            int days = (int) newValue;
            return HozonSettings.setLocationAuthorizedTimeInDays(days);
        }));

        carAccessFineLocationState.setOnPreferenceChangeListener(((preference, newValue) -> {
            boolean checked = (boolean) newValue;
            boolean ok;
            if (checked) {
                ok = PermissionUtils.grantRuntimePermission(Manifest.permission.ACCESS_FINE_LOCATION) == null;
            } else {
                ok = PermissionUtils.revokeRuntimePermission(Manifest.permission.ACCESS_FINE_LOCATION) == null;
            }
            return ok && PermissionUtils.hasRuntimePermission(Manifest.permission.ACCESS_FINE_LOCATION);
        }));

        carAccessCoarseLocationState.setOnPreferenceChangeListener(((preference, newValue) -> {
            boolean checked = (boolean) newValue;
            boolean ok;
            if (checked) {
                ok = PermissionUtils.grantRuntimePermission(Manifest.permission.ACCESS_COARSE_LOCATION) == null;
            } else {
                ok = PermissionUtils.revokeRuntimePermission(Manifest.permission.ACCESS_COARSE_LOCATION) == null;
            }
            return ok && PermissionUtils.hasRuntimePermission(Manifest.permission.ACCESS_COARSE_LOCATION);
        }));
    }

    @Override
    public void onResume() {
        super.onResume();
        refreshData();
    }

    private void refreshData() {
        systemSettingsAvailability.setChecked(PermissionUtils.hasWriteSecureSettingsPermission());
        exemptionsAvailability.setChecked(Exemptions.test());
        permissionManagerAvailability.setChecked(PermissionUtils.getInfo().contains("revokeRuntimePermission"));

        carLocationChecked.setChecked(HozonSettings.isLocationSwitchChecked());
        carLocationAuthorizedDays.setValue(HozonSettings.getLocationAuthorizedTimeInDays());

        carAccessFineLocationState.setChecked(PermissionUtils.hasRuntimePermission(Manifest.permission.ACCESS_FINE_LOCATION));
        carAccessCoarseLocationState.setChecked(PermissionUtils.hasRuntimePermission(Manifest.permission.ACCESS_COARSE_LOCATION));
    }

}
