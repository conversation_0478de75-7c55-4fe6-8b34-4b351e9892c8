package io.github.netamade.ui.fragment;

import android.accessibilityservice.AccessibilityServiceInfo;
import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.util.Log;
import android.view.accessibility.AccessibilityManager;
import android.widget.Toast;

import androidx.annotation.Nullable;
import androidx.preference.PreferenceScreen;

import java.util.List;
import java.util.Set;

import io.github.netamade.AppContext;
import io.github.netamade.AppSettings;
import io.github.netamade.R;
import io.github.netamade.entity.AccessibilityStatusInfo;
import io.github.netamade.ui.preference.AppAccessibilityPreference;
import io.github.netamade.util.ApkUtils;
import io.github.netamade.util.PermissionUtils;
import io.github.netamade.util.SystemAccessibilityUtils;

public class AppKeepAccessibilityFragment extends BasePreferenceFragment implements AppAccessibilityPreference.OnStatusChangedListener {

    public static final String TAG = AppKeepAccessibilityFragment.class.getSimpleName();

    public AppKeepAccessibilityFragment() {
        super(AppContext.applicationContext().getString(R.string.app_keep_accessibility));
    }


    @Override
    public void onCreatePreferences(@Nullable Bundle savedInstanceState, @Nullable String rootKey) {
        addPreferencesFromResource(R.xml.app_keep_accessibility);
    }

    private void refreshData() {
        if (getContext() == null) {
            return;
        }

        PackageManager packageManager = getContext().getPackageManager();

        PreferenceScreen preferenceScreen = getPreferenceScreen();
        preferenceScreen.removeAll();

        boolean permissionDenied = !PermissionUtils.hasWriteSecureSettingsPermission();
        if (permissionDenied) {
            Toast.makeText(getContext(), R.string.system_write_secure_settings_denied, Toast.LENGTH_SHORT).show();
        }

        AccessibilityManager accessibilityManager = (AccessibilityManager) getContext().getSystemService(Context.ACCESSIBILITY_SERVICE);

        List<AccessibilityServiceInfo> installedAccessibilityServiceList = accessibilityManager.getInstalledAccessibilityServiceList();

        Set<String> keepEnabledAccessibilityServiceIds = AppSettings.getKeepEnabledAccessibilityServiceIds();
        Set<String> enabledServiceIds = SystemAccessibilityUtils.getEnabledServiceIds();

        for (AccessibilityServiceInfo serviceInfo : installedAccessibilityServiceList) {

            AccessibilityStatusInfo accessibilityStatusInfo = parse(packageManager, serviceInfo);

            String serviceId = accessibilityStatusInfo.getServiceId();

            if (keepEnabledAccessibilityServiceIds.contains(serviceId)) {

                accessibilityStatusInfo.setKeepEnabled(true);
                accessibilityStatusInfo.setStatus(AccessibilityStatusInfo.Status.KEEP_ENABLED);

            } else if (enabledServiceIds.contains(serviceId)) {

                accessibilityStatusInfo.setKeepEnabled(false);
                accessibilityStatusInfo.setStatus(AccessibilityStatusInfo.Status.ENABLED);

            } else {

                accessibilityStatusInfo.setKeepEnabled(false);
                accessibilityStatusInfo.setStatus(AccessibilityStatusInfo.Status.DISABLED);

            }


            AppAccessibilityPreference preference = new AppAccessibilityPreference(getContext(), accessibilityStatusInfo, this);
            preference.setKey(serviceId);
            preference.setEnabled(!permissionDenied);
            preferenceScreen.addPreference(preference);

        }

    }

    private AccessibilityStatusInfo parse(PackageManager packageManager, AccessibilityServiceInfo serviceInfo) {
        String serviceId = serviceInfo.getId();
        String[] split = serviceId.split("/");

        String packageName = split[0];
        String serviceName;

        if (split[1].startsWith(".")) {
            serviceName = split[0] + split[1];
        } else {
            serviceName = split[1];
        }

        serviceId = packageName + "/" + serviceName;

        AccessibilityStatusInfo status = new AccessibilityStatusInfo();

        status.setPackageName(packageName);
        status.setServiceName(serviceName);
        status.setServiceId(serviceId);
        status.setSettingsActivityName(serviceInfo.getSettingsActivityName());


        try {
            PackageInfo packageInfo = packageManager.getPackageInfo(packageName, 0);
            Drawable appIcon = ApkUtils.loadAppIcon(packageManager, packageInfo, null);

            CharSequence summary = serviceInfo.loadSummary(packageManager);
            if (summary == null) {
                summary = serviceId;
            }
            status.setSummary(summary.toString());

            String serviceDescription = serviceInfo.loadDescription(packageManager);

            if (serviceDescription != null && !serviceDescription.isEmpty()) {

                status.setTitle(packageInfo.applicationInfo.loadLabel(packageManager) + " / " + serviceDescription);

            } else {
                status.setTitle(serviceId);
            }

            status.setAppIcon(appIcon);

        } catch (PackageManager.NameNotFoundException ignored) {

        }

        return status;
    }

    @Override
    public void onResume() {
        super.onResume();
        refreshData();
    }


    @Override
    public void onChanged(AccessibilityStatusInfo status) {
        Set<String> keepEnabledAccessibilityServiceIds = AppSettings.getKeepEnabledAccessibilityServiceIds();
        Set<String> enabledServiceIds = SystemAccessibilityUtils.getEnabledServiceIds();

        String serviceId = status.getServiceId();

        if (AccessibilityStatusInfo.Status.DISABLED.equals(status.getStatus())) {

            if (enabledServiceIds.remove(serviceId)) {
                Log.i(TAG, "disable accessibility serviceId: " + serviceId);
            } else {
                Log.i(TAG, "skip disable accessibility serviceId: " + serviceId);
            }

        } else {

            if (enabledServiceIds.add(serviceId)) {
                Log.i(TAG, "enable accessibility serviceId: " + serviceId);

            } else {
                Log.i(TAG, "skip enable accessibility serviceId: " + serviceId);
            }

        }

        if (status.isKeepEnabled()) {

            if (keepEnabledAccessibilityServiceIds.add(serviceId)) {
                Log.i(TAG, "add keep-enabled serviceId: " + serviceId);
            } else {
                Log.i(TAG, "skip keep-enabled serviceId: " + serviceId);
            }

        } else {

            if (keepEnabledAccessibilityServiceIds.remove(serviceId)) {
                Log.i(TAG, "remove keep-enabled serviceId: " + serviceId);
            } else {
                Log.i(TAG, "skip remove keep-enabled for serviceId: " + serviceId);
            }

        }

        AppSettings.setKeepEnabledAccessibilityServiceIds(keepEnabledAccessibilityServiceIds);
        Log.i(TAG, "keepEnabledAccessibilityServiceIds: " + keepEnabledAccessibilityServiceIds);
        SystemAccessibilityUtils.setEnabledServiceIds(enabledServiceIds);

    }
}
