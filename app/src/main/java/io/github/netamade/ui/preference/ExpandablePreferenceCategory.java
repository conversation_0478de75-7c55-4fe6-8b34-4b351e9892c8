package io.github.netamade.ui.preference;

import android.content.Context;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.widget.ImageView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.preference.Preference;
import androidx.preference.PreferenceCategory;
import androidx.preference.PreferenceViewHolder;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import io.github.netamade.R;

public class ExpandablePreferenceCategory extends PreferenceCategory {
    private Boolean isExpanded = null;
    private boolean alwaysCollapse;
    private final List<String> childKeys = new ArrayList<>();

    public ExpandablePreferenceCategory(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        TypedArray a = context.getTheme().obtainStyledAttributes(
                attrs,
                R.styleable.ExpandablePreferenceCategory, 0, 0);
        String string = a.getString(R.styleable.ExpandablePreferenceCategory_childKeys);
        if (string != null) {
            childKeys.addAll(Arrays.asList(string.split(",")));
        }
        alwaysCollapse = a.getBoolean(R.styleable.ExpandablePreferenceCategory_alwaysCollapse, false);


        setLayoutResource(R.layout.preference_expandable_category);
        if (alwaysCollapse) {
            isExpanded = false;
        }
    }

    public void addChild(String key) {
        this.childKeys.add(key);
    }

    public void removeChild(String key) {
        this.childKeys.remove(key);
    }

    @Override
    public void removeAll() {
        super.removeAll();
        this.childKeys.clear();
    }

    public void toggle() {
        isExpanded = !isExpanded;
        toggleChild();
        getSharedPreferences().edit().putBoolean(getKey() + "_expanded", !alwaysCollapse && isExpanded).apply();

        notifyChanged();
    }

    private void toggleChild() {
        for (String childKey : childKeys) {
            Preference preference = findPreference(childKey);
            if (preference != null) {
                preference.setVisible(isExpanded);
            }
        }
    }

    @Override
    public boolean addPreference(@NonNull Preference preference) {
        if (alwaysCollapse) {
            preference.setVisible(false);
        }
        return super.addPreference(preference);
    }

    @Override
    public void onBindViewHolder(@NonNull PreferenceViewHolder holder) {
        if (isExpanded == null) {
            isExpanded = getSharedPreferences().getBoolean(getKey() + "_expanded", true);
        }
        toggleChild();
        super.onBindViewHolder(holder);
        holder.itemView.setOnClickListener((v) -> toggle());
        ImageView iv = (ImageView) holder.findViewById(R.id.icon);
        iv.setImageResource(isExpanded ? R.drawable.ic_expanded : R.drawable.ic_collapsed);
    }

}
