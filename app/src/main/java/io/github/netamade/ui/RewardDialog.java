package io.github.netamade.ui;

import android.content.Context;
import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatDialog;

import io.github.netamade.R;
import io.github.netamade.databinding.DialogRewardBinding;

public class RewardDialog extends AppCompatDialog {

    private DialogRewardBinding binding;

    public RewardDialog(@NonNull Context context) {
        super(context);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = DialogRewardBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        setTitle(R.string.rewarding);

        updateQRCode(false);

        binding.tabAlipay.setOnClickListener(v -> updateQRCode(true));

        binding.tabWechat.setOnClickListener(v -> updateQRCode(false));

        binding.buttonClose.setOnClickListener(v -> dismiss());
    }

    private void updateQRCode(boolean isAlipay) {
        if (isAlipay) {
            binding.imageQrCode.setImageResource(R.drawable.alipay);
            binding.tabAlipay.setBackgroundResource(R.drawable.tab_background_selected);
            binding.tabWechat.setBackgroundResource(R.drawable.tab_background_unselected);
        } else {
            binding.imageQrCode.setImageResource(R.drawable.wechat);
            binding.tabAlipay.setBackgroundResource(R.drawable.tab_background_unselected);
            binding.tabWechat.setBackgroundResource(R.drawable.tab_background_selected);
        }
    }


}
