package io.github.netamade.ui.dialog;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.os.Handler;
import android.os.Looper;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatDialog;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import io.github.netamade.R;
import io.github.netamade.databinding.DialogLoadUserAppBinding;
import io.github.netamade.entity.AppInfo;
import io.github.netamade.util.ApkUtils;
import lombok.Getter;

public class LoadUserAppDialog extends AppCompatDialog implements Runnable {
    private static final ExecutorService executorService = Executors.newSingleThreadExecutor();
    private final Handler mainHandler;
    @Getter
    private final List<AppInfo> appInfos;

    private final DialogLoadUserAppBinding binding;

    public LoadUserAppDialog(@NonNull Context context) {
        super(context);
        this.mainHandler = new Handler(Looper.getMainLooper());
        this.appInfos = new ArrayList<>();

        binding = DialogLoadUserAppBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        setTitle(R.string.loading);

    }

    public void load() {
        setCancelable(false);
        show();
        executorService.submit(this);
    }

    @SuppressLint("SetTextI18n")
    private void update(int total, int current, String appName, String packageName) {
        mainHandler.post(() -> {
            binding.loadingMessage.setText(current + "/" + total);
            binding.progressMessage.setText(packageName);
            binding.packageName.setText(appName);
            binding.progressBar.setMax(total);
            binding.progressBar.setProgress(current);
        });
    }

    @Override
    public void run() {
        this.appInfos.clear();
        try {

            PackageManager pm = getContext().getPackageManager();
            List<PackageInfo> installedPackages = pm.getInstalledPackages(PackageManager.GET_ACTIVITIES |
                    PackageManager.GET_SERVICES |
                    PackageManager.GET_PROVIDERS |
                    PackageManager.GET_PERMISSIONS);
            int total = installedPackages.size();

            for (int i = 0; i < installedPackages.size(); i++) {
                PackageInfo packageInfo = installedPackages.get(i);
                String appName = packageInfo.applicationInfo.loadLabel(pm).toString();
                update(total, i + 1, appName, packageInfo.packageName);

                AppInfo appInfo = ApkUtils.parseApk(pm, packageInfo);
                appInfos.add(appInfo);

                update(total, i + 1, appName, packageInfo.packageName);

            }

        } catch (Throwable e) {
            e.printStackTrace();
            Toast.makeText(getContext(), R.string.load_user_apps_failed, Toast.LENGTH_SHORT).show();
        } finally {
            mainHandler.post(this::dismiss);
        }
        appInfos.sort((o1, o2) -> {
            if (o1.isSystem() && !o2.isSystem()) {
                return 1;
            }
            if (!o1.isSystem() && o2.isSystem()) {
                return -1;
            }
            if (o1.isShortcutAdded() && !o2.hasShortcuts()) {
                return -1;
            }
            if (!o1.isShortcutAdded() && o2.hasShortcuts()) {
                return 1;
            }
            if (o1.hasShortcuts() && !o2.hasShortcuts()) {
                return -1;
            }
            if (!o1.hasShortcuts() && o2.hasShortcuts()) {
                return 1;
            }
            return o1.getAppName().compareToIgnoreCase(o2.getAppName());
        });
    }
}
