package io.github.netamade.ui.fragment;

import android.content.Context;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CompoundButton;
import android.widget.EditText;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.DefaultItemAnimator;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

import io.github.netamade.AppContext;
import io.github.netamade.R;
import io.github.netamade.databinding.LayoutSyslogDialogBinding;
import io.github.netamade.syslog.LogData;
import io.github.netamade.syslog.SysLogAdapter;
import io.github.netamade.syslog.SysLogCallback;
import io.github.netamade.syslog.SystemLogcatReader;

public class SysLogFragment extends BaseFragment implements CompoundButton.OnCheckedChangeListener, SysLogCallback {

    public SysLogFragment() {
        super(AppContext.applicationContext().getString(R.string.syslog));
    }

    private SysLogAdapter adapter;

    private boolean mIsScrolledToBottom = true;
    private LayoutSyslogDialogBinding binding;
    private final List<LogData> newLogs = new ArrayList<>();
    private final AtomicBoolean inPosting = new AtomicBoolean();


    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = LayoutSyslogDialogBinding.inflate(inflater, container, false);
        binding.cbSyslogEnabled.setEnabled(false);
        binding.cbSyslogEnabled.setOnCheckedChangeListener(this);
        binding.recyclerView.setLayoutManager(new LinearLayoutManager(getContext()));
        binding.recyclerView.setItemAnimator(new DefaultItemAnimator());

        adapter = new SysLogAdapter();
        binding.recyclerView.setAdapter(adapter);
        LinearLayoutManager layoutManager = new LinearLayoutManager(getContext());
        binding.recyclerView.setLayoutManager(layoutManager);

        DividerItemDecoration dividerItemDecoration = new DividerItemDecoration(binding.recyclerView.getContext(),
                layoutManager.getOrientation());
        binding.recyclerView.addItemDecoration(dividerItemDecoration);

        binding.btnClear.setOnClickListener(v -> {
            adapter.clear();
        });

        setupFilter();

        setupRecyclerView(binding.recyclerView);

        return binding.getRoot();
    }

    @Override
    public void onAttach(@NonNull Context context) {
        super.onAttach(context);
    }

    @Override
    public void onDetach() {
        super.onDetach();
        SystemLogcatReader.getOrCreate().removeCallback(this);
    }

    // 初始化方法
    public void setupRecyclerView(RecyclerView recyclerView) {
        // 1. 添加滚动监听
        recyclerView.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrollStateChanged(@NonNull RecyclerView recyclerView, int newState) {
                super.onScrollStateChanged(recyclerView, newState);
                checkIfScrolledToBottom();
            }

            @Override
            public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
                checkIfScrolledToBottom();
            }
        });

        // 2. 注册数据观察者
        adapter.registerAdapterDataObserver(new RecyclerView.AdapterDataObserver() {
            @Override
            public void onItemRangeInserted(int positionStart, int itemCount) {
                if (shouldAutoScroll()) {
                    recyclerView.post(() -> {
                        if (adapter.getItemCount() > 0) {
                            recyclerView.smoothScrollToPosition(adapter.getItemCount() - 1);
                        }
                    });
                }
            }
        });
    }

    public void setupFilter() {
        EditText etFilter = binding.etFilter;
        etFilter.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                String keyword = s.toString();
                adapter.setFilter(log ->
                        log.getRawMessage().toLowerCase().contains(keyword));
            }
        });
    }

    // 判断是否需要自动滚动
    private boolean shouldAutoScroll() {
        return mIsScrolledToBottom && !binding.recyclerView.isComputingLayout();
    }

    // 检查当前是否处于底部
    private void checkIfScrolledToBottom() {
        if (binding.recyclerView.getLayoutManager() == null) return;

        RecyclerView.LayoutManager layoutManager = binding.recyclerView.getLayoutManager();
        int visibleItemCount = layoutManager.getChildCount();
        int totalItemCount = layoutManager.getItemCount();
        int firstVisibleItemPosition = ((LinearLayoutManager) layoutManager)
                .findFirstVisibleItemPosition();

        // 判断逻辑：
        mIsScrolledToBottom = (totalItemCount == 0) ||
                ((firstVisibleItemPosition + visibleItemCount) >= totalItemCount - 1);
    }

    @Override
    public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
        if (isChecked) {
            SystemLogcatReader.getOrCreate().addCallback(this);
        } else {
            SystemLogcatReader.getOrCreate().removeCallback(this);
        }
    }

    @Override
    public void onLogData(LogData logData) {
        if (binding == null) {
            return;
        }
        synchronized (newLogs) {
            newLogs.add(logData);
        }
        if (inPosting.get()) {
            return;
        }
        inPosting.set(true);
        binding.recyclerView.postDelayed(() -> {
            List<LogData> logs = null;
            synchronized (newLogs) {
                logs = new ArrayList<>(newLogs);
                newLogs.clear();
            }
            adapter.append(logs);
            inPosting.set(false);
        }, 300);
    }
}
