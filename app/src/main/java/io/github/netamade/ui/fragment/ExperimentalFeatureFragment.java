package io.github.netamade.ui.fragment;

import android.content.ComponentName;
import android.content.Intent;
import android.os.Bundle;

import androidx.annotation.Nullable;

import io.github.netamade.AppContext;
import io.github.netamade.AppSettings;
import io.github.netamade.R;
import io.github.netamade.util.CaptureRunHelper;
import io.github.netamade.util.HozonSettings;

public class ExperimentalFeatureFragment extends BasePreferenceFragment {


    public ExperimentalFeatureFragment() {
        super(AppContext.applicationContext().getString(R.string.experimental_feature));
    }


    @Override
    public void onCreatePreferences(@Nullable Bundle savedInstanceState, @Nullable String rootKey) {
        addPreferencesFromResource(R.xml.experimental_feature);
        findPreference("hijack_system_map_opening_once").setOnPreferenceClickListener(preference -> {
            AppSettings.setHijackSystemMapOpeningOnce(true);

            CaptureRunHelper.run(getActivity(), () -> {
                Intent intent = new Intent();
                if (!AppSettings.isLaunchNoClearTask()) {
                    intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK);
                    intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
                }
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                intent.setComponent(new ComponentName(HozonSettings.HOZON_MAP_PACKAGE, HozonSettings.HOZON_MAP_ACTIVITY));
                startActivity(intent);
            });

            return true;
        });

        findPreference("swckey_state_auto_unlock").setOnPreferenceClickListener(preference -> {
            Intent intent = new Intent(HozonSettings.ACTION_UPDATE_SWCKEY_STATE);
            intent.putExtra("lock", false);
            getContext().sendBroadcast(intent);
            return true;
        });


        findPreference("swckey_state_auto_lock").setOnPreferenceClickListener(preference -> {
            Intent intent = new Intent(HozonSettings.ACTION_UPDATE_SWCKEY_STATE);
            intent.putExtra("lock", true);
            getContext().sendBroadcast(intent);
            return true;
        });


        findPreference("auto_loudvoice_enabled").setOnPreferenceClickListener(preference -> {
            Intent intent = new Intent(HozonSettings.ACTION_UPDATE_TOYBOX_LOUD_VOICE);
            intent.putExtra("enabled", true);
            getContext().sendBroadcast(intent);
            return true;
        });

        findPreference("auto_loudvoice_disabled").setOnPreferenceClickListener(preference -> {
            Intent intent = new Intent(HozonSettings.ACTION_UPDATE_TOYBOX_LOUD_VOICE);
            intent.putExtra("enabled", false);
            getContext().sendBroadcast(intent);
            return true;
        });

    }

    private void refreshData() {

    }


    @Override
    public void onResume() {
        super.onResume();
        refreshData();
    }


}
