package io.github.netamade.ui.fragment;

import android.content.Context;
import android.os.Bundle;

import androidx.annotation.Nullable;

import io.github.netamade.AppContext;
import io.github.netamade.GrantActivity;
import io.github.netamade.GrantOption;
import io.github.netamade.MainActivity;
import io.github.netamade.R;
import io.github.netamade.util.CaptureRunHelper;

public class AppAutoGrantFragment extends BasePreferenceFragment {


    public AppAutoGrantFragment() {
        super(AppContext.applicationContext().getString(R.string.auto_grant));
    }


    @Override
    public void onCreatePreferences(@Nullable Bundle savedInstanceState, @Nullable String rootKey) {
        addPreferencesFromResource(R.xml.app_auto_grant);

        findPreference("grant_now").setOnPreferenceClickListener(preference -> {

            Context context = preference.getContext();
            CaptureRunHelper.from(MainActivity.class).run(() -> {

                GrantActivity.launch(context, GrantOption.builder()
                        .forceGrant(true)
                        .skipRevokePermission(false)
                        .clearTaskBeforeLaunch(true)
                        .targetMap(null)
                        .launchMapDelay(5)
                        .countdownSeconds(0)
                        .unlockSwckeyState(false)
                        .build());
            });

            return true;
        });

        findPreference("countdown_grant").setOnPreferenceClickListener(preference -> {

            Context context = preference.getContext();
            GrantActivity.launch(context, GrantOption.builder()
                    .forceGrant(true)
                    .skipRevokePermission(false)
                    .clearTaskBeforeLaunch(true)
                    .targetMap(null)
                    .launchMapDelay(5)
                    .countdownSeconds(180)
                    .unlockSwckeyState(false)
                    .build());

            return true;
        });
    }

    private void refreshData() {

    }


    @Override
    public void onResume() {
        super.onResume();
        refreshData();
    }


}
