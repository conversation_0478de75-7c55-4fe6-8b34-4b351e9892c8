package io.github.netamade.ui;

import android.os.Bundle;
import android.view.Gravity;
import android.view.WindowManager;
import android.widget.Toast;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

import io.github.netamade.R;
import io.github.netamade.ui.dialog.AutoCloseableErrorDialog;
import io.github.netamade.util.SystemUtils;

public class CrashActivity extends AppCompatActivity {

    @Override
    protected void onStart() {
        super.onStart();
        int displayHeight = SystemUtils.getDisplayHeight();
        int displayWidth = SystemUtils.getDisplayWidth();

        int height = (int) (displayHeight * 0.65);
        int width = (int) (displayWidth * 0.4);

        WindowManager.LayoutParams params = getWindow().getAttributes();
        params.gravity = Gravity.CENTER;
        params.height = height;
        params.width = width;

        this.getWindow().setAttributes(params);
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_grant);
        String stacktrace = getIntent().getStringExtra("stacktrace");
        Toast.makeText(this, stacktrace, Toast.LENGTH_SHORT).show();
//        new AutoCloseableErrorDialog(this, 30, autoClose -> {
//            finish();
//        })
//                .setTitle("程序出错了，请反馈给作者帮助修复!")
//                .setContent(stacktrace)
//                .show();

    }
}
