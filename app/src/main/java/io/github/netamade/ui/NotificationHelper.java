package io.github.netamade.ui;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.content.Context;
import android.graphics.Color;

import androidx.core.app.NotificationCompat;
import androidx.core.graphics.drawable.IconCompat;

import java.util.concurrent.atomic.AtomicInteger;

import io.github.netamade.AppContext;
import io.github.netamade.R;

public class NotificationHelper {
    public static final String CHANNEL_ID = "netamde";
    public static final String CHANNEL_NAME = "netamde";

    private static AtomicInteger id = new AtomicInteger(1000);
    private static final NotificationManager notificationManager = AppContext.applicationContext().getSystemService(NotificationManager.class);

    static {
        NotificationChannel chan = new NotificationChannel(CHANNEL_ID, CHANNEL_NAME, NotificationManager.IMPORTANCE_LOW);

        chan.setLightColor(Color.BLUE);
        chan.setLockscreenVisibility(Notification.VISIBILITY_PUBLIC);
        chan.setSound(null, null);

        notificationManager.createNotificationChannel(chan);
    }

    public static int generateId() {
        return id.getAndIncrement();
    }

    public static void send(int id, Notification notification) {
        notificationManager.notify(id, notification);
    }

    public static Notification createNotification(String title, String content) {
        Context context = AppContext.applicationContext();
        return new NotificationCompat.Builder(context, CHANNEL_ID)
                .setAutoCancel(true)
                .setContentText(content)
                .setContentTitle(title)
                .setSmallIcon(IconCompat.createWithResource(context, R.drawable.ic_launcher))
//                .setSilent(true)
                .build();
    }

}
