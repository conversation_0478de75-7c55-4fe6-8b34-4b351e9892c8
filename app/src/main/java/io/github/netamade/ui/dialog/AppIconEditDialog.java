package io.github.netamade.ui.dialog;

import android.content.Context;
import android.view.LayoutInflater;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AlertDialog;

import com.google.android.material.dialog.MaterialAlertDialogBuilder;

import io.github.netamade.databinding.DialogAppIconEditBinding;
import io.github.netamade.entity.AppIconItem;

public class AppIconEditDialog {
    private final Context context;
    private DialogAppIconEditBinding binding;
    private AlertDialog dialog;
    private AppIconItem appIconItem;
    private OnSaveListener onSaveListener;
    private OnDeleteListener onDeleteListener;

    public interface OnSaveListener {
        void onSave(AppIconItem item);
    }

    public interface OnDeleteListener {
        void onDelete();
    }

    public AppIconEditDialog(@NonNull Context context, AppIconItem appIconItem) {
        this.context = context;
        this.appIconItem = appIconItem;
        initDialog();
    }

    private void initDialog() {
        binding = DialogAppIconEditBinding.inflate(LayoutInflater.from(context));
        
        // Populate fields with current values
        binding.editAppName.setText(appIconItem.getAppName());
        binding.editPackageName.setText(appIconItem.getPackageName());
        binding.editClassName.setText(appIconItem.getClassName());
        binding.editIcon.setText(appIconItem.getIcon());
        binding.switchIsUsed.setChecked(appIconItem.isUsed());
        binding.switchCloudConfigFlag.setChecked(appIconItem.isCloudConfigFlag());
        binding.switchRedPointFlag.setChecked(appIconItem.isRedPointFlag());
        binding.switchIsNewAdd.setChecked(appIconItem.isNewAdd());
    }

    public void setOnSaveListener(OnSaveListener listener) {
        this.onSaveListener = listener;
    }

    public void setOnDeleteListener(OnDeleteListener listener) {
        this.onDeleteListener = listener;
    }

    public void show() {
        MaterialAlertDialogBuilder builder = new MaterialAlertDialogBuilder(context);
        builder.setTitle("编辑应用图标")
                .setView(binding.getRoot())
                .setPositiveButton("保存", (dialog, which) -> {
                    saveChanges();
                    if (onSaveListener != null) {
                        onSaveListener.onSave(appIconItem);
                    }
                })
                .setNegativeButton("取消", null)
                .setNeutralButton("删除", (dialog, which) -> {
                    if (onDeleteListener != null) {
                        onDeleteListener.onDelete();
                    }
                });

        dialog = builder.create();
        dialog.show();
    }

    private void saveChanges() {
        appIconItem.setAppName(binding.editAppName.getText().toString().trim());
        appIconItem.setPackageName(binding.editPackageName.getText().toString().trim());
        appIconItem.setClassName(binding.editClassName.getText().toString().trim());
        appIconItem.setIcon(binding.editIcon.getText().toString().trim());
        appIconItem.setUsed(binding.switchIsUsed.isChecked());
        appIconItem.setCloudConfigFlag(binding.switchCloudConfigFlag.isChecked());
        appIconItem.setRedPointFlag(binding.switchRedPointFlag.isChecked());
        appIconItem.setNewAdd(binding.switchIsNewAdd.isChecked());
    }
}
