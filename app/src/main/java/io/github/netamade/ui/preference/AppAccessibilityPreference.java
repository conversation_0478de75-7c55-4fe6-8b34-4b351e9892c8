package io.github.netamade.ui.preference;

import android.content.Context;

import androidx.annotation.NonNull;
import androidx.preference.Preference;
import androidx.preference.PreferenceViewHolder;

import io.github.netamade.R;
import io.github.netamade.databinding.PreferenceAccessibilityControlBinding;
import io.github.netamade.entity.AccessibilityStatusInfo;
import io.github.netamade.util.HozonSettings;

public class AppAccessibilityPreference extends Preference {


    private final OnStatusChangedListener listener;

    public interface OnStatusChangedListener {
        void onChanged(AccessibilityStatusInfo status);
    }

    private final AccessibilityStatusInfo statusInfo;

    public AppAccessibilityPreference(Context context, AccessibilityStatusInfo statusInfo, OnStatusChangedListener listener) {
        super(context);
        this.statusInfo = statusInfo;
        this.listener = listener;

        setLayoutResource(R.layout.preference_accessibility_control);

    }

    @Override
    public void onBindViewHolder(@NonNull PreferenceViewHolder holder) {
        PreferenceAccessibilityControlBinding binding = PreferenceAccessibilityControlBinding.bind(holder.itemView);

        if (statusInfo.getAppIcon() != null) {
            binding.icon.setImageDrawable(statusInfo.getAppIcon());
        } else {
            binding.icon.setImageResource(R.drawable.ic_unknown);
        }
        binding.title.setText(statusInfo.getTitle());
        binding.summary.setText(statusInfo.getSummary());

        String settingsActivityName = statusInfo.getSettingsActivityName();
        if (settingsActivityName != null && !settingsActivityName.isEmpty()) {
            binding.getRoot().setOnClickListener(v -> HozonSettings.startActivity(statusInfo.getPackageName(), settingsActivityName));
        }
        AccessibilityStatusInfo.Status status = statusInfo.getStatus();
        if (AccessibilityStatusInfo.Status.KEEP_ENABLED.equals(status)) {
            binding.toggleButton.check(R.id.toggle_keeping_status);
        } else if (AccessibilityStatusInfo.Status.ENABLED.equals(status)) {
            binding.toggleButton.check(R.id.toggle_enabled);
        } else {
            binding.toggleButton.check(R.id.toggle_disabled);
        }

        binding.toggleButton.addOnButtonCheckedListener((group, checkedId, isChecked) -> {

            if (!isChecked) {
                return;
            }

            if (R.id.toggle_disabled == checkedId) {

                statusInfo.setKeepEnabled(false);
                statusInfo.setStatus(AccessibilityStatusInfo.Status.DISABLED);

                listener.onChanged(statusInfo);

            } else if (R.id.toggle_keeping_status == checkedId) {

                statusInfo.setKeepEnabled(true);
                statusInfo.setStatus(AccessibilityStatusInfo.Status.KEEP_ENABLED);
                listener.onChanged(statusInfo);

            } else {

                statusInfo.setKeepEnabled(false);
                statusInfo.setStatus(AccessibilityStatusInfo.Status.ENABLED);
                listener.onChanged(statusInfo);

            }

        });

    }
}
