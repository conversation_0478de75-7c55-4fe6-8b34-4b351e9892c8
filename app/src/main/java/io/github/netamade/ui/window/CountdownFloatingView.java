package io.github.netamade.ui.window;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.PixelFormat;
import android.util.AttributeSet;
import android.view.GestureDetector;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.WindowManager;
import android.widget.FrameLayout;

import androidx.annotation.NonNull;

import io.github.netamade.AppSettings;
import io.github.netamade.R;
import io.github.netamade.databinding.LayoutCountdownBinding;
import io.github.netamade.util.SystemUtils;
import io.github.netamade.util.SuspendableCountDownTimer;
import lombok.Setter;

/**
 * 倒计时执行
 */
public class CountdownFloatingView extends FrameLayout {

    public static final String COUNT_DOWN_FLOATING_VIEW_X = "countdown_floating_view_x";
    public static final String COUNT_DOWN_FLOATING_VIEW_Y = "countdown_floating_view_y";

    public interface OnCountdownListener {
        void onCancel();

        void onExecute(boolean reach);
    }

    private final int seconds;

    private LayoutCountdownBinding binding;
    private SuspendableCountDownTimer countDownTimer;
    private WindowManager wm;
    private WindowManager.LayoutParams layoutParams;
    private float initialX, initialY, initialTouchX, initialTouchY;

    @Setter
    private OnCountdownListener onCountdownListener;
    private GestureDetector gestureDetector = null;


    private boolean stopped = false;

    public CountdownFloatingView(Context context, int seconds) {
        this(context, null, seconds);
    }

    public CountdownFloatingView(Context context, AttributeSet attributeSet, int seconds) {
        super(context, attributeSet);
        this.seconds = seconds;
        initViews();
        initGesture();
    }


    public void hide() {
        stopped = true;
        try {
            wm.removeView(this);
            countDownTimer.cancel();
        } catch (Throwable ignore) {

        }
    }

    public void show() {
        hide();
        stopped = false;
        wm.addView(this, layoutParams);

        countDownTimer = new SuspendableCountDownTimer(seconds * 1000L, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
                int secondsRemaining = (int) (millisUntilFinished / 1000) + 1;
                binding.text.setText(String.valueOf(secondsRemaining));
            }

            @Override
            public void onFinish() {
                hide();
                if (onCountdownListener != null) {
                    onCountdownListener.onExecute(true);
                }
            }
        };
        countDownTimer.start();
    }

    private void initViews() {
        binding = LayoutCountdownBinding.inflate(LayoutInflater.from(getContext()));
        addView(binding.getRoot(), SystemUtils.dpToPx(200), SystemUtils.dpToPx(200));

        wm = getContext().getSystemService(WindowManager.class);

        layoutParams = new WindowManager.LayoutParams();
        layoutParams.type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY;
        layoutParams.format = PixelFormat.RGBA_8888;
        layoutParams.flags = WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL
                | WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE | WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH;
        layoutParams.gravity = Gravity.START | Gravity.TOP;
        layoutParams.width = WindowManager.LayoutParams.WRAP_CONTENT;
        layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT;

        layoutParams.x = AppSettings.getPrefs().getInt(COUNT_DOWN_FLOATING_VIEW_X, 20);
        layoutParams.y = AppSettings.getPrefs().getInt(COUNT_DOWN_FLOATING_VIEW_Y, 40);

        updateSuspendStatus(false);
    }

    private void updateSuspendStatus(boolean suspend) {
        if (suspend) {
            binding.text.setTextColor(getContext().getColor(R.color.colorError));
            binding.title.setText(R.string.countdown_suspended);
        } else {
            binding.text.setTextColor(getContext().getColor(R.color.white));
            binding.title.setText(R.string.countdown_execute);
        }
    }

    private void initGesture() {
        gestureDetector = new GestureDetector(getContext(), simpleOnGestureListener);
    }

    private final GestureDetector.SimpleOnGestureListener simpleOnGestureListener = new GestureDetector.SimpleOnGestureListener() {
        @Override
        public void onLongPress(@NonNull MotionEvent e) {
            if (stopped) {
                return;
            }
            hide();
            countDownTimer.cancel();
            if (onCountdownListener != null) {
                onCountdownListener.onCancel();
            }
        }

        @Override
        public boolean onDoubleTap(@NonNull MotionEvent e) {
            if (stopped) {
                return false;
            }
            hide();
            countDownTimer.cancel();
            if (onCountdownListener != null) {
                onCountdownListener.onExecute(false);
                return true;
            }
            return false;
        }

        @Override
        public boolean onSingleTapConfirmed(@NonNull MotionEvent e) {
            boolean suspend = !countDownTimer.isSuspend();
            updateSuspendStatus(suspend);
            countDownTimer.setSuspend(suspend);
            return super.onSingleTapConfirmed(e);
        }
    };


    @SuppressLint("ClickableViewAccessibility")
    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (stopped) {
            return false;
        }
        if (gestureDetector.onTouchEvent(event)) {
            return true;
        }

        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                initialX = layoutParams.x;
                initialY = layoutParams.y;
                initialTouchX = event.getRawX();
                initialTouchY = event.getRawY();
                break;
            case MotionEvent.ACTION_MOVE:
                layoutParams.x = (int) (initialX + event.getRawX() - initialTouchX);
                layoutParams.y = (int) (initialY + event.getRawY() - initialTouchY);
                wm.updateViewLayout(this, layoutParams);
                AppSettings.getPrefs().edit()
                        .putInt(COUNT_DOWN_FLOATING_VIEW_X, layoutParams.x)
                        .putInt(COUNT_DOWN_FLOATING_VIEW_Y, layoutParams.y)
                        .apply();
                break;
        }
        return true;
    }
}
