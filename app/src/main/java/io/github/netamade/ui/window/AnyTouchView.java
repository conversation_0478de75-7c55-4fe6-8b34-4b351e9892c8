package io.github.netamade.ui.window;

import android.annotation.SuppressLint;
import android.content.Context;
import android.view.LayoutInflater;

import androidx.annotation.NonNull;

import io.github.netamade.AppSettings;
import io.github.netamade.databinding.LayoutAnytouchBinding;

@SuppressLint("ViewConstructor")
public class AnyTouchView extends BaseFloatingView {


    public static final String TAG = AnyTouchView.class.getSimpleName();
    private LayoutAnytouchBinding binding;

    public AnyTouchView(Context context, boolean isLeft, @NonNull FloatingViewListener floatingViewListener, int displayId) {
        super(context, isLeft, floatingViewListener, displayId);
        binding = LayoutAnytouchBinding.inflate(LayoutInflater.from(context), this, true);
        refreshVisible();
    }

    public void refreshVisible() {
        boolean visible = AppSettings.isAnyTouchViewShowBackground();
        if (visible) {
            setAlpha(0.7f);
        } else {
            setAlpha(0);
        }
    }


}
