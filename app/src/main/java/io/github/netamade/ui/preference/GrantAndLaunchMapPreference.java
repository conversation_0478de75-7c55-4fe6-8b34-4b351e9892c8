package io.github.netamade.ui.preference;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.util.AttributeSet;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.preference.Preference;
import androidx.preference.PreferenceViewHolder;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import java.util.ArrayList;
import java.util.List;

import io.github.netamade.AppSettings;
import io.github.netamade.GrantActivity;
import io.github.netamade.GrantOption;
import io.github.netamade.R;
import io.github.netamade.databinding.LayoutQuickLaunchBinding;
import io.github.netamade.databinding.PreferenceGrantAndLaunchMapBinding;
import io.github.netamade.entity.MapType;
import io.github.netamade.ui.dialog.AutoCloseableErrorDialog;
import io.github.netamade.util.HozonSettings;
import io.github.netamade.util.NetworkUtils;
import lombok.AllArgsConstructor;
import lombok.Getter;

public class GrantAndLaunchMapPreference extends Preference {
    private static final String TAG = GrantAndLaunchMapPreference.class.getSimpleName();

    public GrantAndLaunchMapPreference(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        setLayoutResource(R.layout.preference_grant_and_launch_map);
    }


    @Override
    public void onBindViewHolder(@NonNull PreferenceViewHolder holder) {
        super.onBindViewHolder(holder);

        PreferenceGrantAndLaunchMapBinding binding = PreferenceGrantAndLaunchMapBinding.bind(holder.itemView);

        Context context = getContext();

        binding.recyclerView.setLayoutManager(new GridLayoutManager(context, 5));

        ArrayList<QuickLaunchItem> items = new ArrayList<>();

        for (MapType map : MapType.values()) {
            items.add(new QuickLaunchItem(map.getKey(), map.getName(), map.getIcon(), (v) -> {

                GrantActivity.launch(v.getContext(), GrantOption.builder()
                        .forceGrant(true)
                        .skipRevokePermission(false)
                        .clearTaskBeforeLaunch(true)
                        .targetMap(map)
                        .launchMapDelay(5)
                        .countdownSeconds(0)
                        .unlockSwckeyState(false)
                        .build());

            }));
        }

        items.add(new QuickLaunchItem("item_unlock_swckey_state", context.getString(R.string.swckey_state_auto_unlock), "ic_unlock", (v) -> {
            Intent intent = new Intent(HozonSettings.ACTION_UPDATE_SWCKEY_STATE);
            intent.putExtra("lock", false);
            getContext().sendBroadcast(intent);
        }));


        items.add(new QuickLaunchItem("item_open_loud_voice", context.getString(R.string.auto_loudvoice_enabled), "ic_speaker", (v) -> {
            Intent intent = new Intent(HozonSettings.ACTION_UPDATE_TOYBOX_LOUD_VOICE);
            intent.putExtra("enabled", true);
            getContext().sendBroadcast(intent);
        }));

        items.add(new QuickLaunchItem("item_block_ai_speech_daemon_network", context.getString(R.string.block_aispeech_network), "ic_ai_speech_network_blocked", (v) -> {
            HozonSettings.blockAISpeechDaemonNetwork();
        }));

        items.add(new QuickLaunchItem("item_unblock_ai_speech_daemon_network", context.getString(R.string.unblock_aispeech_network), "ic_ai_speech_network_unblocked", (v) -> {
            HozonSettings.unblockAISpeechDaemonNetwork();
        }));

        items.add(new QuickLaunchItem("item_enable_adb_over_wifi", context.getString(R.string.adb_over_wifi), "ic_adb", (v) -> {
            String result = HozonSettings.enableAdbOverWifi();
            List<String> ips = NetworkUtils.getAllIp();
            StringBuilder sb = new StringBuilder();
            sb.append("本机 IP 地址列表:\n");
            for (String ip : ips) {
                sb.append(ip).append('\n');
            }
            sb.append('\n');
            sb.append("\nADB 端口: ");
            sb.append(result);
            AutoCloseableErrorDialog dialog = new AutoCloseableErrorDialog(v.getContext(), 60, (x) -> {

            });
            dialog.setTitle("无线 ADB 开启结果")
                    .setContent(sb.toString())
                    .show();
        }));


        List<String> orders = AppSettings.getQuickLaunchOrders();

        Log.i(TAG, "load quick launch orders: " + orders);

        items.sort(((o1, o2) -> {

            int ind1 = orders.indexOf(o1.getKey());
            int ind2 = orders.indexOf(o2.getKey());

            if (ind1 < 0) {
                ind1 = Integer.MAX_VALUE;
            }
            if (ind2 < 0) {
                ind2 = Integer.MAX_VALUE;
            }

            return ind1 - ind2;

        }));

        Adapter adapter = new Adapter(context, items);
        binding.recyclerView.setAdapter(adapter);

    }

    static class Adapter extends RecyclerView.Adapter<Adapter.Holder> {

        private final Context context;
        private final List<QuickLaunchItem> items;

        public Adapter(Context context, List<QuickLaunchItem> items) {
            this.context = context;
            this.items = items;
        }

        private void pinOrders(QuickLaunchItem item, int pos) {

            List<String> orders = AppSettings.getQuickLaunchOrders();

            String key = item.getKey();
            orders.remove(key);
            orders.add(0, key);

            items.remove(pos);
            items.add(0, item);

            AppSettings.setQuickLaunchOrders(orders);

            Log.i(TAG, "change quick launch orders: " + AppSettings.getQuickLaunchOrders());

            notifyItemMoved(pos, 0);
        }


        @NonNull
        @Override
        public Adapter.Holder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            LayoutQuickLaunchBinding binding = LayoutQuickLaunchBinding.inflate(LayoutInflater.from(context));
            return new Holder(binding.getRoot(), binding);
        }

        @Override
        @SuppressLint("DiscouragedApi")
        public void onBindViewHolder(@NonNull Adapter.Holder holder, int position) {
            QuickLaunchItem item = items.get(position);
            holder.binding.text.setText(item.getTitle());
            int resourceId = context.getResources().getIdentifier(item.getIcon(), "drawable", context.getPackageName());
            holder.binding.image.setImageResource(resourceId);

            holder.binding.getRoot().setOnClickListener(v -> {
                int pos = holder.getBindingAdapterPosition();

                QuickLaunchItem it = items.get(pos);

                if (it.getOnClickListener() != null) {
                    it.getOnClickListener().onClick(v);
                }

            });

            holder.binding.getRoot().setOnLongClickListener(v -> {
                int pos = holder.getBindingAdapterPosition();
                QuickLaunchItem it = items.get(pos);

                pinOrders(it, holder.getBindingAdapterPosition());
                return true;
            });
        }

        @Override
        public int getItemCount() {
            return items.size();
        }

        static class Holder extends RecyclerView.ViewHolder {

            private final LayoutQuickLaunchBinding binding;

            public Holder(@NonNull View itemView, LayoutQuickLaunchBinding binding) {
                super(itemView);
                this.binding = binding;
            }
        }
    }

    @Getter
    @AllArgsConstructor
    static class QuickLaunchItem {
        String key;
        String title;
        String icon;
        View.OnClickListener onClickListener;
    }

}
