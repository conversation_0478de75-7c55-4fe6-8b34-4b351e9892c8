package io.github.netamade.ui.window;

import android.annotation.SuppressLint;
import android.content.Context;
import android.util.Log;
import android.view.LayoutInflater;
import android.widget.FrameLayout;

import androidx.recyclerview.widget.GridLayoutManager;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.Set;

import io.github.netamade.AppSettings;
import io.github.netamade.adapter.AppShortcutAdapter;
import io.github.netamade.databinding.LayoutAppShortcutBinding;
import io.github.netamade.entity.AppShortcut;

@SuppressLint("ViewConstructor")
public class AppShortcutView extends FrameLayout {

    public static final String TAG = AppShortcutView.class.getSimpleName();
    private LayoutAppShortcutBinding binding;

    public AppShortcutView(Context context, int displayId, AppShortcutAdapter.OnCloseListener onCloseListener) {
        super(context);
        binding = LayoutAppShortcutBinding.inflate(LayoutInflater.from(context), this, true);
        binding.recyclerView.setLayoutManager(new GridLayoutManager(context, 3));

        ArrayList<AppShortcut> appShortcuts = new ArrayList<>();

        Set<String> values = AppSettings.getAnyTouchAppShortCuts();

        Log.i(TAG, values.toString());

        for (String value : values) {
            appShortcuts.add(AppShortcut.from(value));
        }
        appShortcuts = new ArrayList<>(new HashSet<>(appShortcuts));

        AppShortcutAdapter adapter = new AppShortcutAdapter(context, appShortcuts, onCloseListener);
        binding.recyclerView.setAdapter(adapter);
    }
}
