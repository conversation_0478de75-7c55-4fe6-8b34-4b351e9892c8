package io.github.netamade.ui.preference;

import android.content.Context;
import android.util.AttributeSet;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.preference.Preference;

import io.github.netamade.R;

public class HighlightPreferenceScreen extends Preference {


    public HighlightPreferenceScreen(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        setLayoutResource(R.layout.preference_highlight_screen);
    }


}
