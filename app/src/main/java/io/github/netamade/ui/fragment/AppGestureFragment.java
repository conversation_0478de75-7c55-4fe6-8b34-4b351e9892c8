package io.github.netamade.ui.fragment;

import android.os.Bundle;
import android.widget.Toast;

import androidx.annotation.Nullable;

import java.util.HashSet;
import java.util.Set;

import io.github.netamade.AppContext;
import io.github.netamade.R;

public class AppGestureFragment extends BasePreferenceFragment {

    public AppGestureFragment() {
        super(AppContext.applicationContext().getString(R.string.any_touch_gesture));
    }


    @Override
    public void onCreatePreferences(@Nullable Bundle savedInstanceState, @Nullable String rootKey) {
        addPreferencesFromResource(R.xml.app_gesture);
        findPreference("any_touch_view_trigger_area").setOnPreferenceChangeListener((preference, newValue) -> {
            Set<String> entryValues = (HashSet<String>) newValue;
            if (entryValues == null || entryValues.isEmpty()) {
                Toast.makeText(preference.getContext(), R.string.must_at_least_choice_one, Toast.LENGTH_SHORT).show();
                return false;
            }
            return true;
        });
    }

    private void refreshData() {
    }


    @Override
    public void onResume() {
        super.onResume();
        refreshData();
    }


}
