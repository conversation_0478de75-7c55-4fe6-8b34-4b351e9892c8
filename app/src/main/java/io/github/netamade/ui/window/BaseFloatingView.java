package io.github.netamade.ui.window;

import android.annotation.SuppressLint;
import android.content.Context;
import android.view.Display;
import android.view.GestureDetector;
import android.view.MotionEvent;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;

import org.jetbrains.annotations.NotNull;

@SuppressLint("ViewConstructor")
public class BaseFloatingView extends LinearLayout {
    private static final int SWIPE_THRESHOLD = 5;
    private static final int SWIPE_VELOCITY_THRESHOLD = 5;
    private final FloatingViewListener floatingViewListener;
    private final boolean left2right;
    private GestureDetector mGestureDetector;

    protected final int displayId;

    public BaseFloatingView(Context context, boolean left2right, @NotNull FloatingViewListener floatingViewListener) {
        this(context, left2right, floatingViewListener, Display.DEFAULT_DISPLAY);
    }

    public BaseFloatingView(Context context, boolean left2right, @NotNull FloatingViewListener floatingViewListener, int displayId) {
        super(context);
        this.displayId = displayId;
        this.left2right = left2right;
        this.floatingViewListener = floatingViewListener;
        mGestureDetector = new GestureDetector(context, gestureListener);
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        return mGestureDetector.onTouchEvent(event) || super.onTouchEvent(event);
    }

    GestureDetector.SimpleOnGestureListener gestureListener = new GestureDetector.SimpleOnGestureListener() {

        @Override
        public boolean onFling(MotionEvent e1, MotionEvent e2, float velocityX, float velocityY) {
            float diffX = e2.getX() - e1.getX();
            float diffY = e2.getY() - e1.getY();

            if (Math.abs(diffX) > Math.abs(diffY)) {
                // 水平滑动
                if (Math.abs(diffX) > SWIPE_THRESHOLD && Math.abs(velocityX) > SWIPE_VELOCITY_THRESHOLD) {
                    if (diffX > 0) {
                        floatingViewListener.onEvent(left2right ? FloatingViewListener.Event.SWIPE_RIGHT : FloatingViewListener.Event.SWIPE_LEFT, left2right, displayId);
                    } else {
                        floatingViewListener.onEvent(left2right ? FloatingViewListener.Event.SWIPE_LEFT : FloatingViewListener.Event.SWIPE_RIGHT, left2right, displayId);
                    }
                    return true;
                }
            } else {
                // 垂直滑动
                if (Math.abs(diffY) > SWIPE_THRESHOLD && Math.abs(velocityY) > SWIPE_VELOCITY_THRESHOLD) {
                    if (diffY > 0) {
                        floatingViewListener.onEvent(FloatingViewListener.Event.SWIPE_DOWN, left2right, displayId);
                    } else {
                        floatingViewListener.onEvent(FloatingViewListener.Event.SWIPE_UP, left2right, displayId);
                    }
                    return true;
                }
            }
            return false;
        }

        @Override
        public boolean onSingleTapConfirmed(@NonNull MotionEvent e) {
            floatingViewListener.onEvent(FloatingViewListener.Event.SINGLE_TAP, left2right, displayId);
            return true;
        }

        @Override
        public void onLongPress(@NonNull MotionEvent e) {
            super.onLongPress(e);
            floatingViewListener.onEvent(FloatingViewListener.Event.LONG_PRESS, left2right, displayId);
        }

        @Override
        public boolean onDoubleTap(@NonNull MotionEvent e) {
            floatingViewListener.onEvent(FloatingViewListener.Event.DOUBLE_TAP, left2right, displayId);
            return super.onDoubleTap(e);
        }
    };
}
