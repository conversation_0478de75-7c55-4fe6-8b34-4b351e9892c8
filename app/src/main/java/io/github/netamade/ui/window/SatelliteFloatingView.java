package io.github.netamade.ui.window;

import android.Manifest;
import android.annotation.SuppressLint;
import android.content.Context;
import android.content.pm.PackageManager;
import android.graphics.PixelFormat;
import android.location.GnssStatus;
import android.location.Location;
import android.location.LocationListener;
import android.location.LocationManager;
import android.os.Handler;
import android.os.Looper;
import android.util.AttributeSet;
import android.view.GestureDetector;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.WindowManager;
import android.widget.FrameLayout;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.core.app.ActivityCompat;

import java.util.Locale;
import java.util.Timer;
import java.util.TimerTask;
import java.util.concurrent.TimeUnit;

import io.github.netamade.AppSettings;
import io.github.netamade.GrantActivity;
import io.github.netamade.GrantOption;
import io.github.netamade.R;
import io.github.netamade.databinding.LayoutSatelliteMonitorBinding;
import io.github.netamade.util.SystemUtils;

/**
 * 卫星数量悬浮窗
 */
public class SatelliteFloatingView extends FrameLayout implements LocationListener {

    public static final String COUNT_DOWN_FLOATING_VIEW_X = "satellite_floating_view_x";
    public static final String COUNT_DOWN_FLOATING_VIEW_Y = "satellite_floating_view_y";

    private LayoutSatelliteMonitorBinding binding;
    private Timer timer;
    private WindowManager wm;
    private WindowManager.LayoutParams layoutParams;
    private float initialX, initialY, initialTouchX, initialTouchY;

    private GestureDetector gestureDetector = null;
    private LocationManager locationManager;
    private final Handler handler;

    private transient long lastZeroSatelliteTime = 0;

    private boolean stopped = false;

    private final GnssStatus.Callback gnssCallback = new GnssStatus.Callback() {
        @Override
        public void onSatelliteStatusChanged(@NonNull GnssStatus status) {
            super.onSatelliteStatusChanged(status);
            int usedSatellites = 0;
            int totalSatellites = status.getSatelliteCount();
            if (totalSatellites == 0) {
                if (lastZeroSatelliteTime == 0) {
                    lastZeroSatelliteTime = System.currentTimeMillis();
                }

                if (System.currentTimeMillis() - lastZeroSatelliteTime > TimeUnit.SECONDS.toSeconds(AppSettings.getSatelliteAutoGrantSeconds())) {
                    Toast.makeText(getContext(), R.string.re_grant_because_no_satellite, Toast.LENGTH_SHORT).show();

                    GrantActivity.launch(getContext(), GrantOption.builder()
                            .forceGrant(true)
                            .skipRevokePermission(false)
                            .clearTaskBeforeLaunch(true)
                            .targetMap(null)
                            .launchMapDelay(0)
                            .countdownSeconds(5)
                            .unlockSwckeyState(false)
                            .build());

                }
            } else {
                lastZeroSatelliteTime = 0;
            }

            for (int i = 0; i < totalSatellites; i++) {
                if (status.usedInFix(i)) {
                    usedSatellites++;
                }
            }
            String satelliteCountText = String.format(Locale.US, "%d / %d", usedSatellites, totalSatellites);
            binding.text.setText(satelliteCountText);
        }
    };


    public SatelliteFloatingView(Context context) {
        this(context, null);
    }

    public SatelliteFloatingView(Context context, AttributeSet attributeSet) {
        super(context, attributeSet);
        handler = new Handler(Looper.getMainLooper());
        initViews();
        initGesture();
    }


    public void hide() {
        stopped = true;
        try {
            locationManager.unregisterGnssStatusCallback(gnssCallback);
            wm.removeView(this);
            timer.cancel();
        } catch (Throwable ignore) {

        }
    }

    public void show() {
        hide();
        stopped = false;
        wm.addView(this, layoutParams);

        timer = new Timer("upload-satellite-count");

        locationManager = (LocationManager) getContext().getSystemService(Context.LOCATION_SERVICE);
        if (ActivityCompat.checkSelfPermission(getContext(), Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED && ActivityCompat.checkSelfPermission(getContext(), Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
            Toast.makeText(getContext(), R.string.no_location_permission, Toast.LENGTH_SHORT).show();
            hide();
            AppSettings.setSatelliteViewEnabled(false);
            return;
        }

        locationManager.registerGnssStatusCallback(gnssCallback, handler);
        timer.schedule(new TimerTask() {
            @Override
            public void run() {

                handler.post(() -> {
                    if (ActivityCompat.checkSelfPermission(getContext(), Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED && ActivityCompat.checkSelfPermission(getContext(), Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
                        return;
                    }
                    locationManager.requestLocationUpdates(LocationManager.GPS_PROVIDER, 30000, 0, SatelliteFloatingView.this);
                });
            }
        }, 500, 3000);
    }

    private void initViews() {
        binding = LayoutSatelliteMonitorBinding.inflate(LayoutInflater.from(getContext()));
        addView(binding.getRoot(), SystemUtils.dpToPx(120), SystemUtils.dpToPx(120));

        wm = getContext().getSystemService(WindowManager.class);

        layoutParams = new WindowManager.LayoutParams();
        layoutParams.type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY;
        layoutParams.format = PixelFormat.RGBA_8888;
        layoutParams.flags = WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL
                | WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE | WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH;
        layoutParams.gravity = Gravity.START | Gravity.TOP;
        layoutParams.width = WindowManager.LayoutParams.WRAP_CONTENT;
        layoutParams.height = WindowManager.LayoutParams.WRAP_CONTENT;

        layoutParams.x = AppSettings.getPrefs().getInt(COUNT_DOWN_FLOATING_VIEW_X, 20);
        layoutParams.y = AppSettings.getPrefs().getInt(COUNT_DOWN_FLOATING_VIEW_Y, 40);

        updateSuspendStatus(false);
    }

    private void updateSuspendStatus(boolean suspend) {
        if (suspend) {
            binding.text.setTextColor(getContext().getColor(R.color.colorError));
        } else {
            binding.text.setTextColor(getContext().getColor(R.color.white));
        }
    }

    private void initGesture() {
        gestureDetector = new GestureDetector(getContext(), simpleOnGestureListener);
    }

    private final GestureDetector.SimpleOnGestureListener simpleOnGestureListener = new GestureDetector.SimpleOnGestureListener() {
        @Override
        public void onLongPress(@NonNull MotionEvent e) {
            if (stopped) {
                return;
            }
            hide();
            timer.cancel();
        }

        @Override
        public boolean onDoubleTap(@NonNull MotionEvent e) {
            if (stopped) {
                return false;
            }
            hide();
            timer.cancel();
            return true;
        }

        @Override
        public boolean onSingleTapConfirmed(@NonNull MotionEvent e) {
            return super.onSingleTapConfirmed(e);
        }
    };


    @SuppressLint("ClickableViewAccessibility")
    @Override
    public boolean onTouchEvent(MotionEvent event) {
        if (stopped) {
            return false;
        }
        if (gestureDetector.onTouchEvent(event)) {
            return true;
        }

        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
                initialX = layoutParams.x;
                initialY = layoutParams.y;
                initialTouchX = event.getRawX();
                initialTouchY = event.getRawY();
                break;
            case MotionEvent.ACTION_MOVE:
                layoutParams.x = (int) (initialX + event.getRawX() - initialTouchX);
                layoutParams.y = (int) (initialY + event.getRawY() - initialTouchY);
                wm.updateViewLayout(this, layoutParams);
                AppSettings.getPrefs().edit()
                        .putInt(COUNT_DOWN_FLOATING_VIEW_X, layoutParams.x)
                        .putInt(COUNT_DOWN_FLOATING_VIEW_Y, layoutParams.y)
                        .apply();
                break;
        }
        return true;
    }

    @Override
    public void onLocationChanged(@NonNull Location location) {

    }
}
