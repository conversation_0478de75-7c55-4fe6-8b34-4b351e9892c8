package io.github.netamade.ui.fragment;

import android.os.Bundle;

import androidx.annotation.Nullable;
import androidx.preference.PreferenceFragmentCompat;

public class BasePreferenceFragment extends PreferenceFragmentCompat implements Titleable {
    private final String title;

    public BasePreferenceFragment(String title) {
        this.title = title;
    }

    @Override
    public String getTitle() {
        return title;
    }


    @Override
    public void onCreatePreferences(@Nullable Bundle savedInstanceState, @Nullable String rootKey) {

    }
}
