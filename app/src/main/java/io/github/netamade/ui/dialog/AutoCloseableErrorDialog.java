package io.github.netamade.ui.dialog;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.DialogInterface;
import android.os.CountDownTimer;
import android.view.LayoutInflater;
import android.widget.Button;

import androidx.appcompat.app.AlertDialog;

import com.google.android.material.dialog.MaterialAlertDialogBuilder;

import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;

import io.github.netamade.R;
import io.github.netamade.databinding.LayoutConsoleLogBinding;

public class AutoCloseableErrorDialog extends CountDownTimer {
    private final Consumer<Boolean> onClose;
    private Button button;
    private AlertDialog errorDialog;
    private final Context context;
    private String title = null;
    private String content = null;

    public AutoCloseableErrorDialog(Context context, int seconds, Consumer<Boolean> onClose) {
        super(TimeUnit.SECONDS.toMillis(seconds), 1000);
        this.context = context;
        this.onClose = onClose;
    }

    public AutoCloseableErrorDialog setContent(String content) {
        this.content = content;
        return this;
    }

    public AutoCloseableErrorDialog setTitle(String title) {
        this.title = title;
        return this;
    }

    public AlertDialog show() {
        LayoutConsoleLogBinding binding = LayoutConsoleLogBinding.inflate(LayoutInflater.from(context));
        binding.text.setText(content);

        MaterialAlertDialogBuilder builder = new MaterialAlertDialogBuilder(context);
        if (title == null) {
            builder.setTitle(R.string.exception_title);
        }else {
            builder.setTitle(title);
        }
        this.errorDialog = builder
                .setView(binding.getRoot())
                .setCancelable(false)
                .setNegativeButton(R.string.close, (dialog, which) -> {
                    if (this.onClose != null) {
                        this.onClose.accept(false);
                    }
                    cancel();
                    dialog.dismiss();
                }).show();

        start();
        this.button = errorDialog.getButton(DialogInterface.BUTTON_NEGATIVE);
        return errorDialog;
    }

    @SuppressLint("SetTextI18n")
    @Override
    public void onTick(long millisUntilFinished) {
        this.button.setText(context.getString(R.string.close) + " (" + TimeUnit.MILLISECONDS.toSeconds(millisUntilFinished) + "S)");
    }

    @Override
    public void onFinish() {
        if (this.errorDialog.isShowing()) {
            this.errorDialog.dismiss();
        }
        if (this.onClose != null) {
            this.onClose.accept(true);
        }
    }
}