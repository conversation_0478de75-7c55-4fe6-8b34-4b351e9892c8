package io.github.netamade.ui.window;

import android.annotation.SuppressLint;
import android.content.Context;
import android.view.LayoutInflater;

import androidx.annotation.NonNull;

import io.github.netamade.databinding.LayoutAnybackBinding;
import io.github.netamade.display.DisplayHelper;
import lombok.Getter;

@SuppressLint("ViewConstructor")
public class AnyBackView extends BaseFloatingView {


    public static final String TAG = AnyBackView.class.getSimpleName();

    private LayoutAnybackBinding binding;
    @Getter
    private final DisplayHelper.DisplayInfo displayInfo;

    public AnyBackView(Context context, DisplayHelper.DisplayInfo displayInfo, @NonNull FloatingViewListener floatingViewListener) {
        super(context, true, floatingViewListener);
        this.displayInfo = displayInfo;
        binding = LayoutAnybackBinding.inflate(LayoutInflater.from(context), this, true);
        binding.text.setText("屏幕ID-" + displayInfo.getDisplay().getDisplayId());
    }


}
