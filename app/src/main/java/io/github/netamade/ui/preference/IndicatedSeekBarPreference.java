package io.github.netamade.ui.preference;

import android.content.Context;
import android.util.AttributeSet;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.preference.Preference;
import androidx.preference.SeekBarPreference;

/**
 * 支持 format summary 的 SeekBarPreference
 */
public class IndicatedSeekBarPreference extends SeekBarPreference implements Preference.OnPreferenceChangeListener {
    private final String originSummary;
    private OnPreferenceChangeListener mOnChangeListener;

    public IndicatedSeekBarPreference(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        this.originSummary = getSummary() == null ? null : getSummary().toString();
        super.setOnPreferenceChangeListener(this);
    }

    @Override
    public void setValue(int seekBarValue) {
        super.setValue(seekBarValue);
    }

    @Nullable
    @Override
    public CharSequence getSummary() {
        CharSequence val = super.getSummary();
        if (originSummary != null) {
            return String.format(originSummary, getValue());
        }
        return val;
    }

    @Override
    public void setOnPreferenceChangeListener(@Nullable OnPreferenceChangeListener onPreferenceChangeListener) {
        mOnChangeListener = onPreferenceChangeListener;
    }

    @Override
    public boolean onPreferenceChange(@NonNull Preference preference, Object newValue) {
        setSummary(getSummary());
        if (mOnChangeListener != null) {
            return mOnChangeListener.onPreferenceChange(preference, newValue);
        }
        return true;
    }
}
