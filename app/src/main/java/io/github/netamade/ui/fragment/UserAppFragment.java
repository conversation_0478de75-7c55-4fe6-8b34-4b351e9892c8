package io.github.netamade.ui.fragment;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CompoundButton;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.DividerItemDecoration;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import io.github.netamade.AppContext;
import io.github.netamade.R;
import io.github.netamade.adapter.AppsAdapter;
import io.github.netamade.databinding.LayoutUserAppBinding;
import io.github.netamade.entity.AppInfo;
import io.github.netamade.ui.dialog.LoadUserAppDialog;

public class UserAppFragment extends BaseFragment implements CompoundButton.OnCheckedChangeListener {

    public UserAppFragment() {
        super(AppContext.applicationContext().getString(R.string.user_apps_list));
    }

    private RecyclerView recyclerView;
    private AppsAdapter appsAdapter;
    private List<AppInfo> appInfos;
    private LayoutUserAppBinding binding;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = LayoutUserAppBinding.inflate(inflater, container, false);
        binding.showNoActivitiesApp.setOnCheckedChangeListener(this);
        binding.showUserApp.setOnCheckedChangeListener(this);
        binding.showSystemApp.setOnCheckedChangeListener(this);
        this.recyclerView = binding.recyclerView;
        this.appsAdapter = new AppsAdapter(getContext(), new ArrayList<>());

        LinearLayoutManager layoutManager = new LinearLayoutManager(getContext());
        this.recyclerView.setLayoutManager(layoutManager);

        DividerItemDecoration dividerItemDecoration = new DividerItemDecoration(recyclerView.getContext(),
                layoutManager.getOrientation());
        recyclerView.addItemDecoration(dividerItemDecoration);

        this.recyclerView.setAdapter(appsAdapter);
        loadAppList();
        return binding.getRoot();
    }

    private void refreshData(int[] count) {
        binding.appSummary.setText(getString(R.string.app_summary, count[0], count[1], count[2], count[3], count[4]));
        binding.appSummary.setVisibility(View.VISIBLE);
        this.appsAdapter.updateAppList(appInfos);
    }

    public void loadAppList() {
        binding.appSummary.setVisibility(View.INVISIBLE);
        boolean showUser = binding.showUserApp.isChecked();
        boolean showSystem = binding.showSystemApp.isChecked();
        boolean showNoActivities = binding.showNoActivitiesApp.isChecked();
        int[] count = new int[5];
        LoadUserAppDialog loadUserAppDialog = new LoadUserAppDialog(getContext());

        loadUserAppDialog.setOnDismissListener(dialog -> {

            appInfos = ((LoadUserAppDialog) dialog).getAppInfos();
            Iterator<AppInfo> iterator = appInfos.iterator();

            count[0] = appInfos.size();

            while (iterator.hasNext()) {
                AppInfo next = iterator.next();

                if (next.isSystem()) {
                    count[1]++;
                } else {
                    count[2]++;
                }
                if (!next.hasShortcuts()) {
                    count[3]++;
                }

                if (!showUser && !next.isSystem()) {
                    iterator.remove();
                } else if (!showSystem && next.isSystem()) {
                    iterator.remove();
                } else if (!showNoActivities && !next.hasShortcuts()) {
                    iterator.remove();
                }
            }

            count[4] = appInfos.size();

            refreshData(count);

        });

        loadUserAppDialog.load();
    }

    @Override
    public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
        loadAppList();
    }
}
