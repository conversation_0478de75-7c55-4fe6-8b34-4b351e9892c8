package io.github.netamade;

import android.util.Log;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import io.github.netamade.entity.MapType;
import lombok.val;

public class MapTypeHelper {

    public static final String TAG = MapTypeHelper.class.getSimpleName();

    private static final Map<MapType, String> recordLastActivities = new LinkedHashMap<>();

    public static String getLastActivity(String packageName) {
        Optional<MapType> mapOpt = MapType.values().stream().filter(m -> m.getPackageName().equals(packageName)).findFirst();
        return mapOpt.map(recordLastActivities::get).orElse(null);
    }

    public static void recordActivity(String packageName, String className) {

        Optional<MapType> mapOpt = MapType.values().stream().filter(m -> m.getPackageName().equals(packageName)).findFirst();
        if (!mapOpt.isPresent()) {
            return;
        }
        val map = mapOpt.get();

        Set<String> activities = map.getActivities();
        if (className.charAt(0) == '.') {
            className = packageName + className;
        }

        if (activities.contains(className)) {

            Log.i(TAG, "recordActivity " + className);

            recordLastActivities.put(map, className);

        }

    }
}
