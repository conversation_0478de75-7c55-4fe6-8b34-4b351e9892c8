package io.github.netamade;

import androidx.appcompat.app.AppCompatActivity;

public class BaseActivity extends AppCompatActivity {
    @Override
    protected void onStart() {
        super.onStart();
//        int displayHeight = SystemUtils.getDisplayHeight();
//        int displayWidth = SystemUtils.getDisplayWidth();
//        if (displayHeight > displayWidth) {
//            int requestedOrientation = getRequestedOrientation();
//            setRequestedOrientation(requestedOrientation == ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE ? ActivityInfo.SCREEN_ORIENTATION_PORTRAIT : ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE);
//        }
    }
}
