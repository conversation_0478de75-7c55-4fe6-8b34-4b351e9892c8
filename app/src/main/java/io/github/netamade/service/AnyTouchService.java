package io.github.netamade.service;

import android.accessibilityservice.AccessibilityService;
import android.annotation.SuppressLint;
import android.app.Notification;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.SharedPreferences;
import android.database.ContentObserver;
import android.graphics.PixelFormat;
import android.os.Process;
import android.provider.Settings;
import android.util.Log;
import android.view.Display;
import android.view.Gravity;
import android.view.MotionEvent;
import android.view.View;
import android.view.WindowManager;
import android.view.accessibility.AccessibilityEvent;
import android.view.accessibility.AccessibilityNodeInfo;
import android.widget.Toast;

import androidx.annotation.Nullable;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;

import io.github.netamade.AMap;
import io.github.netamade.AppContext;
import io.github.netamade.AppSettings;
import io.github.netamade.BaiduMap;
import io.github.netamade.MainActivity;
import io.github.netamade.MapTypeHelper;
import io.github.netamade.R;
import io.github.netamade.TencentMap;
import io.github.netamade.display.DisplayHelper;
import io.github.netamade.syslog.LogData;
import io.github.netamade.syslog.SysLogCallback;
import io.github.netamade.ui.NotificationHelper;
import io.github.netamade.ui.window.AnyTouchView;
import io.github.netamade.ui.window.AppShortcutView;
import io.github.netamade.ui.window.FloatingViewListener;
import io.github.netamade.util.HozonSettings;
import io.github.netamade.util.Shell;
import io.github.netamade.util.SystemAccessibilityUtils;
import io.github.netamade.util.SystemUtils;
import lombok.Getter;

@SuppressLint("DefaultLocale")
public class AnyTouchService extends AccessibilityService implements FloatingViewListener, SharedPreferences.OnSharedPreferenceChangeListener, SysLogCallback {

    static class WindowHolder {
        WindowManager windowManager;
        List<AnyTouchView> anyTouchViews = new ArrayList<>();
        AppShortcutView appShortcutView;

        List<View> addedViews = new ArrayList<>();
    }

    public static final String TAG = "AnyTouchService";
    private final Map<Integer, WindowHolder> windowManagerByDisplay = new HashMap<>();
    private static boolean isRunning = false;
    private static boolean isServiceConnected = false;

    public static final int NOTIFICATION_ID = NotificationHelper.generateId();

    @Getter
    private static final Map<String, GestureAction> gestureActions = new HashMap<>();

    private final LinkedList<LogData> logBuffer = new LinkedList<>();

    /**
     * -1 什么都不做，0解锁 1锁定
     */
    private final AtomicInteger swckeyStateFlag = new AtomicInteger(-1);

    /**
     * -1 什么都不做，0关闭 1 打开
     */
    private final AtomicInteger loudVoiceFlag = new AtomicInteger(-1);


    private final ContentObserver accessibilityServiceContentObserver = new ContentObserver(AppContext.mainHandler()) {
        @Override
        public void onChange(boolean selfChange) {
            if (selfChange) {
                return;
            }
            SystemAccessibilityUtils.keepAccessibilityServiceAlive();
        }
    };

    private final BroadcastReceiver internalReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {

            String action = intent.getAction();
            if (!isServiceConnected || action == null) {
                Toast.makeText(context, R.string.any_touch_service_offline, Toast.LENGTH_SHORT).show();
                return;
            }

            switch (action) {
                case HozonSettings.ACTION_UPDATE_TOYBOX_LOUD_VOICE:

                    boolean isEnabled = intent.getBooleanExtra("enabled", false);
                    Log.i(TAG, "received: " + action + " => " + isEnabled);
                    if (isEnabled) {
                        loudVoiceFlag.set(1);
                    } else {
                        loudVoiceFlag.set(0);
                    }

                    try {
                        HozonSettings.startActivity(HozonSettings.HOZON_TOYBOX_PACKAGE, HozonSettings.HOZON_TOYBOX_MAIN_ACTIVITY);
                    } catch (Exception e) {
                        Toast.makeText(context, context.getString(R.string.failed_to_update_loud_voice, e.getMessage()), Toast.LENGTH_SHORT).show();
                    }

                    break;
                case HozonSettings.ACTION_UPDATE_SWCKEY_STATE:

                    boolean isLock = intent.getBooleanExtra("lock", false);
                    Log.i(TAG, "received: " + action + " => " + isLock);
                    if (isLock) {
                        swckeyStateFlag.set(1);
                    } else {
                        swckeyStateFlag.set(0);
                    }

                    try {
                        HozonSettings.startActivity(HozonSettings.HOZON_FACTORY_MODE_PACKAGE, HozonSettings.HOZON_FACTORY_HARD_KEY_ACTIVITY);
                    } catch (Exception e) {
                        Toast.makeText(context, context.getString(R.string.failed_to_update_swckey_state, e.getMessage()), Toast.LENGTH_SHORT).show();
                    }

                    break;
            }


        }
    };


    public static void reEnabledAccessibilityService() {
        if (isRunning) {
            return;
        }
        Log.i(TAG, "re-enable accessibility service for service: " + AnyTouchService.class.getSimpleName());

        Context context = AppContext.applicationContext();
        String accessibilityService = context.getPackageName() + "/" + AnyTouchService.class.getCanonicalName();

        Set<String> enabledServiceIds = SystemAccessibilityUtils.getEnabledServiceIds();
        HashSet<String> tmp = new HashSet<>(enabledServiceIds);
        tmp.add(accessibilityService);

        if (enabledServiceIds.remove(accessibilityService)) {
            SystemAccessibilityUtils.setEnabledServiceIds(enabledServiceIds);

            AppContext.post(() -> {
                SystemAccessibilityUtils.setEnabledServiceIds(tmp);
            }, 50);

        } else {
            SystemAccessibilityUtils.setEnabledServiceIds(tmp);
        }


    }

    @Override
    public void onAccessibilityEvent(AccessibilityEvent event) {
        isRunning = true;

        CharSequence pkg = event.getPackageName();
        if (pkg == null) {
            return;
        }

        CharSequence className = event.getClassName();

        int swckeyState = swckeyStateFlag.get();
        if (swckeyState != -1 && HozonSettings.HOZON_FACTORY_MODE_PACKAGE.contentEquals(pkg)) {
            if (className != null && HozonSettings.HOZON_FACTORY_HARD_KEY_ACTIVITY.contentEquals(className)) {
                Log.i(TAG, "开始设置方控 swckeyState: " + swckeyState);

                try {

                    AccessibilityNodeInfo rootNode = getRootInActiveWindow();
                    if (rootNode != null) {

                        String btnId = swckeyState == 0 ? HozonSettings.HOZON_FACTORY_UNLOCK_BUTTON_ID : HozonSettings.HOZON_FACTORY_LOCK_BUTTON_ID;

                        List<AccessibilityNodeInfo> nodeInfosByViewId = rootNode.findAccessibilityNodeInfosByViewId(btnId);

                        if (nodeInfosByViewId == null || nodeInfosByViewId.isEmpty()) {
                            return;
                        }

                        AccessibilityNodeInfo buttonNode = nodeInfosByViewId.get(0);
                        if (buttonNode != null) {

                            Log.i(TAG, "方控 点击按键测试按钮按钮: " + btnId);

                            buttonNode.performAction(AccessibilityNodeInfo.ACTION_CLICK);
                        }
                        nodeInfosByViewId = rootNode.findAccessibilityNodeInfosByViewId(HozonSettings.HOZON_FACTORY_BACK_BUTTON_ID);

                        if (nodeInfosByViewId == null || nodeInfosByViewId.isEmpty()) {
                            return;
                        }

                        buttonNode = nodeInfosByViewId.get(0);
                        if (buttonNode != null) {

                            Log.i(TAG, "方控 点击返回按钮: " + HozonSettings.HOZON_FACTORY_BACK_BUTTON_ID);

                            buttonNode.performAction(AccessibilityNodeInfo.ACTION_CLICK);
                        }

                    }

                } finally {
                    Log.i(TAG, "方控设置结束");
                    swckeyStateFlag.set(-1);
                }

            }


        }

        int loudVoiceState = loudVoiceFlag.get();

        if (loudVoiceState != -1 && HozonSettings.HOZON_TOYBOX_PACKAGE.contentEquals(pkg)) {
            if (className != null && HozonSettings.HOZON_TOYBOX_MAIN_ACTIVITY.contentEquals(className)) {
                Log.i(TAG, "设置大嗓门 已进入哪吒工具箱主页面, 点击 哪吒大嗓门 按钮");


                AccessibilityNodeInfo rootNode = getRootInActiveWindow();
                if (rootNode != null) {

                    String btnId = HozonSettings.HOZON_TOYBOX_MAIN_LOUD_VOICE_BUTTON_ID;

                    List<AccessibilityNodeInfo> nodeInfosByViewId = rootNode.findAccessibilityNodeInfosByViewId(HozonSettings.HOZON_TOYBOX_MAIN_LOUD_VOICE_BUTTON_ID);

                    if (nodeInfosByViewId == null || nodeInfosByViewId.isEmpty()) {
                        return;
                    }

                    for (AccessibilityNodeInfo node : nodeInfosByViewId) {
                        if ("哪吒大嗓门".contentEquals(node.getText())) {
                            Log.i(TAG, "设置大嗓门 点击主界面的大嗓门按钮: " + btnId);
                            node.getParent().performAction(AccessibilityNodeInfo.ACTION_CLICK);
                        }
                    }

                }


            } else if (className != null && HozonSettings.HOZON_TOYBOX_LOUD_VOICE_ACTIVITY.contentEquals(className)) {
                Log.i(TAG, "设置大嗓门 已进入哪吒大嗓门主页面, 即将进行开启 loudVoiceState: " + loudVoiceState);

                try {

                    AccessibilityNodeInfo rootNode = getRootInActiveWindow();
                    if (rootNode != null) {

                        String btnId = HozonSettings.HOZON_TOYBOX_LOUD_VOICE_SPEAKER_BUTTON_ID;

                        List<AccessibilityNodeInfo> nodeInfosByViewId = rootNode.findAccessibilityNodeInfosByViewId(btnId);

                        if (nodeInfosByViewId == null || nodeInfosByViewId.isEmpty()) {
                            return;
                        }

                        for (AccessibilityNodeInfo node : nodeInfosByViewId) {
                            CharSequence contentDescription = node.getContentDescription();

                            int currentState = "关闭外放多媒体".contentEquals(contentDescription) ? 1 : 0;
                            Log.i(TAG, "设置大嗓门 currentState: " + currentState + " contentDescription: " + contentDescription);

                            if (currentState == loudVoiceState) {
                                Toast.makeText(this, "大嗓门已经是" + (currentState == 1 ? "打开状态" : "关闭状态"), Toast.LENGTH_SHORT).show();
                                Log.i(TAG, "设置大嗓门 当前状态与目标状态一致: " + contentDescription);
                            } else {
                                Log.i(TAG, "设置大嗓门 点击按钮: " + contentDescription);
                                node.performAction(AccessibilityNodeInfo.ACTION_CLICK);
                            }
                        }

                    }

                } finally {
                    loudVoiceFlag.set(-1);
                    AppContext.post(() -> {

                        AppContext.post(() -> {
                            performGlobalAction(GLOBAL_ACTION_BACK);
                        }, 300);

                        performGlobalAction(GLOBAL_ACTION_BACK);
                    }, 300);
                }

            }
        }

        if (event.getEventType() != AccessibilityEvent.TYPE_WINDOW_STATE_CHANGED) {
            return;
        }

        if (className != null) {
            MapTypeHelper.recordActivity(pkg.toString(), className.toString());
        }

        String packageName = pkg.toString();

        Log.d(TAG, "onAccessibilityEvent => " + packageName);


        if (HozonSettings.HOZON_MAP_PACKAGE.equals(packageName)) {
            sendBroadcast(new Intent(HozonSettings.ACTION_LAUNCH_HOZON_MAP));
        }

        if ("com.google.android.apps.nexuslauncher".equals(packageName) || "com.github.kr328.clash.MainActivity".contentEquals(className)) {
            Log.i(TAG, "send custom ACTION_LAUNCH_HOZON_MAP");
            sendBroadcast(new Intent(HozonSettings.ACTION_LAUNCH_HOZON_MAP));
        }

    }


    @SuppressLint({"WrongConstant", "InlinedApi"})
    @Override
    public void onCreate() {
        super.onCreate();
        Log.i(TAG, "onCreate userId1=" + Process.myUserHandle());

        Notification notification = NotificationHelper.createNotification(getString(R.string.app_name), getString(R.string.any_touch_service_creating));
        NotificationHelper.send(NOTIFICATION_ID, notification);

//        ServiceCompat.startForeground(this, 1, notification, ServiceInfo.FOREGROUND_SERVICE_TYPE_MEDIA_PLAYBACK);
        startForeground(1, notification);

        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(HozonSettings.ACTION_UPDATE_SWCKEY_STATE);
        intentFilter.addAction(HozonSettings.ACTION_UPDATE_TOYBOX_LOUD_VOICE);
        registerReceiver(internalReceiver, intentFilter, RECEIVER_NOT_EXPORTED);

        Log.i(TAG, "onCreate");
        getContentResolver().registerContentObserver(Settings.Secure.getUriFor(Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES), true, accessibilityServiceContentObserver);
        if (!isRunning) {
            Log.i(TAG, "onCreate reEnabledAccessibilityService");
            reEnabledAccessibilityService();
        }

        gestureActions.clear();

        gestureActions.put("back", (isLeft, displayId) -> {
            if (displayId == Display.DEFAULT_DISPLAY) {
                performGlobalAction(GLOBAL_ACTION_BACK);
            } else {
                Shell.root("input", "-d", displayId + "", "keyevent", "KEYCODE_BACK");
            }
        });
        gestureActions.put("app_shortcut", this::showAppShortcut);
        gestureActions.put("home", (isLeft, displayId) -> {
            if (displayId == Display.DEFAULT_DISPLAY) {
                performGlobalAction(GLOBAL_ACTION_HOME);
            } else {
                Shell.root("input", "-d", displayId + "", "keyevent", "KEYCODE_HOME");
            }
        });

        gestureActions.put("hozon_map", (isLeft, displayId) -> {
            if (displayId != Display.DEFAULT_DISPLAY) {
                Toast.makeText(this, "副屏不支持启动此操作", Toast.LENGTH_SHORT).show();
                return;
            }
            HozonSettings.startActivity(HozonSettings.HOZON_MAP_PACKAGE, HozonSettings.HOZON_MAP_ACTIVITY);
        });
        gestureActions.put("@baidu_map", (isLeft, displayId) -> {
            if (displayId != Display.DEFAULT_DISPLAY) {
                Toast.makeText(this, "副屏不支持启动此操作", Toast.LENGTH_SHORT).show();
                return;
            }
            Intent intent = new Intent(AnyTouchService.this, BaiduMap.class);
            intent.putExtra("force-grant", true);

            startActivity(intent);
        });
        gestureActions.put("@tencent_map", (isLeft, displayId) -> {
            if (displayId != Display.DEFAULT_DISPLAY) {
                Toast.makeText(this, "副屏不支持启动此操作", Toast.LENGTH_SHORT).show();
                return;
            }
            Intent intent = new Intent(AnyTouchService.this, TencentMap.class);
            intent.putExtra("force-grant", true);
            startActivity(intent);
        });
        gestureActions.put("@a_map", (isLeft, displayId) -> {
            if (displayId != Display.DEFAULT_DISPLAY) {
                Toast.makeText(this, "副屏不支持启动此操作", Toast.LENGTH_SHORT).show();
                return;
            }
            Intent intent = new Intent(AnyTouchService.this, AMap.class);
            intent.putExtra("force-grant", true);
            startActivity(intent);
        });
        gestureActions.put("@a_map_mobile", (isLeft, displayId) -> {
            if (displayId != Display.DEFAULT_DISPLAY) {
                Toast.makeText(this, "副屏不支持启动此操作", Toast.LENGTH_SHORT).show();
                return;
            }
            Intent intent = new Intent(AnyTouchService.this, AMap.class);
            intent.putExtra("force-grant", true);

            startActivity(intent);
        });
        gestureActions.put(getPackageName(), (isLeft, displayId) -> {
            if (displayId != Display.DEFAULT_DISPLAY) {
                Toast.makeText(this, "副屏不支持启动此操作", Toast.LENGTH_SHORT).show();
                return;
            }
            HozonSettings.startActivity(getPackageName(), MainActivity.class.getName());
        });

    }


    @Override
    protected void onServiceConnected() {
        super.onServiceConnected();
        isRunning = true;
        isServiceConnected = true;

        removeAllViews();

        AccessibilityNodeInfo rootInActiveWindow = getRootInActiveWindow();
        if (rootInActiveWindow != null && rootInActiveWindow.getPackageName() != null) {
//            lastPackageName = rootInActiveWindow.getPackageName().toString();
        }

        AppSettings.getPrefs().registerOnSharedPreferenceChangeListener(this);

        showAnyTouchView();

        Notification notification = NotificationHelper.createNotification(getString(R.string.app_name), getString(R.string.any_touch_service_available));
        NotificationHelper.send(NOTIFICATION_ID, notification);
    }

    @Override
    public void onInterrupt() {
        isRunning = false;
        isServiceConnected = false;
        Notification notification = NotificationHelper.createNotification(getString(R.string.app_name), getString(R.string.any_touch_service_offline));
        NotificationHelper.send(NOTIFICATION_ID, notification);
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        unregisterReceiver(internalReceiver);
        removeAllViews();
        getContentResolver().unregisterContentObserver(accessibilityServiceContentObserver);
        AppSettings.getPrefs().unregisterOnSharedPreferenceChangeListener(this);
    }

    private void removeAllViews() {
        windowManagerByDisplay.forEach((id, h) -> {
            removeView(h.windowManager, h.appShortcutView);
            for (View view : h.addedViews) {
                removeView(h.windowManager, view);
            }
            h.addedViews.clear();
            h.anyTouchViews.clear();
            h.appShortcutView = null;
        });

    }


    private void removeView(WindowManager windowManager, View view) {
        if (view != null) {
            try {
                windowManager.removeViewImmediate(view);
            } catch (Throwable ignored) {
            }
        }
    }

    private void performGestureAction(String action, boolean isLeft, int displayId) {
        Log.i(TAG, "AnyTouchView performGestureAction displayId=" + displayId + " left=" + isLeft + " action=" + action);
        GestureAction gestureAction = gestureActions.get(action);

        if (gestureAction != null) {
            try {

                gestureAction.action(isLeft, displayId);

            } catch (Throwable e) {
                Log.e(TAG, "执行手势失败", e);
            }
        }
    }


    @Override
    public void onEvent(Event event, boolean left2right, int displayId) {
        switch (event) {
            case SWIPE_RIGHT:
                performGestureAction(AppSettings.getAnyTouchViewSwipeRight(), left2right, displayId);
                break;
            case SWIPE_UP:
                performGestureAction(AppSettings.getAnyTouchViewSwipeUp(), left2right, displayId);
                break;
            case SWIPE_DOWN:
                performGestureAction(AppSettings.getAnyTouchViewSwipeDown(), left2right, displayId);
                break;
            case SWIPE_LEFT:
                performGestureAction(AppSettings.getAnyTouchViewSwipeLeft(), left2right, displayId);
                break;
            case DOUBLE_TAP:
                performGestureAction(AppSettings.getAnyTouchViewDoubleTap(), left2right, displayId);
                break;
            case LONG_PRESS:
                performGestureAction(AppSettings.getAnyTouchViewLongPress(), left2right, displayId);
                break;
        }
    }


    @SuppressLint("ClickableViewAccessibility")
    private void showAppShortcut(boolean isLeft, int displayId) {
        WindowHolder holder = windowManagerByDisplay.get(displayId);
        if (holder == null) {
            return;
        }
        WindowManager windowManager = holder.windowManager;
        if (windowManager == null) {
            return;
        }

        Log.i(TAG, "showAppShortcut in display=" + displayId);

        AppShortcutView lastAppShortcutView = holder.appShortcutView;
        if (holder.appShortcutView != null) {
            removeView(windowManager, lastAppShortcutView);
        }

        WindowManager.LayoutParams appShortcutLayoutParams = new WindowManager.LayoutParams();

        appShortcutLayoutParams.type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY;

        appShortcutLayoutParams.format = PixelFormat.RGBA_8888;

        appShortcutLayoutParams.flags = WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL
                | WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE | WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH;

        if (isLeft) {
            appShortcutLayoutParams.gravity = Gravity.START | Gravity.CENTER_VERTICAL;
        } else {
            appShortcutLayoutParams.gravity = Gravity.END | Gravity.CENTER_VERTICAL;
        }

        appShortcutLayoutParams.width = WindowManager.LayoutParams.WRAP_CONTENT;
        appShortcutLayoutParams.height = (int) (SystemUtils.getDisplayHeight() * 0.3);

        appShortcutLayoutParams.x = 50;

        holder.appShortcutView = new AppShortcutView(this, displayId, () -> {
            removeView(windowManager, holder.appShortcutView);
        });

        holder.appShortcutView.setOnTouchListener((v, event) -> {
            if (event.getAction() == MotionEvent.ACTION_OUTSIDE) {
                removeView(windowManager, holder.appShortcutView);
                return true;
            }
            return false;
        });

        windowManager.addView(holder.appShortcutView, appShortcutLayoutParams);
    }

    private void showAnyTouchView() {
        if (!Settings.canDrawOverlays(this)) {
            Toast.makeText(this, R.string.system_alert_windows_permission_denied, Toast.LENGTH_LONG).show();
            return;
        }
        if (!AppSettings.isAnyTouchViewEnabled()) {
            return;
        }
        Log.i(TAG, "showAnyTouchView");

        DisplayHelper.DisplayInfo[] displayInfos = DisplayHelper.getDisplayInfos();

        for (DisplayHelper.DisplayInfo displayInfo : displayInfos) {

            int displayId = displayInfo.getDisplay().getDisplayId();
            if (displayId != Display.DEFAULT_DISPLAY && !AppSettings.isAnyTouchViewInSecondaryDisplayEnabled()) {
                Log.i(TAG, "skip secondary display any touch view");
                continue;
            }

            WindowManager windowsManager = displayInfo.getWindowsManager();

            windowManagerByDisplay.computeIfPresent(displayId, (id, holder) -> {
                removeView(windowsManager, holder.appShortcutView);
                for (AnyTouchView anyTouchView : holder.anyTouchViews) {
                    removeView(windowsManager, anyTouchView);
                }
                return null;
            });

            WindowHolder holder = new WindowHolder();
            holder.windowManager = windowsManager;
            windowManagerByDisplay.put(displayId, holder);

            Log.i(TAG, displayInfo.getDisplay().toString());

            Set<String> areas = AppSettings.getAnyTouchViewTriggerArea();

            for (String area : areas) {

                boolean isLeft = "left".equals(area);

                int heightPercent = AppSettings.getAnyTouchViewHeight();
                int width = AppSettings.getAnyTouchViewWidth();

                WindowManager.LayoutParams anyTouchLayoutParams = new WindowManager.LayoutParams();

                anyTouchLayoutParams.type = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY;

                anyTouchLayoutParams.format = PixelFormat.RGBA_8888;

                anyTouchLayoutParams.flags = WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL
                        | WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE;

                if (isLeft) {
                    anyTouchLayoutParams.gravity = Gravity.START | Gravity.CENTER_VERTICAL;
                } else {
                    anyTouchLayoutParams.gravity = Gravity.END | Gravity.CENTER_VERTICAL;
                }

                anyTouchLayoutParams.x = 0;
                anyTouchLayoutParams.y = 0;

                anyTouchLayoutParams.width = width;
                anyTouchLayoutParams.height = (int) (SystemUtils.getDisplayHeight() * (heightPercent / 100.0));

                AnyTouchView anyTouchView = new AnyTouchView(this, isLeft, this, displayId);

                holder.anyTouchViews.add(anyTouchView);
                holder.addedViews.add(anyTouchView);
                windowsManager.addView(anyTouchView, anyTouchLayoutParams);

                Log.i(TAG, "add anytouch view in display=" + displayId + " left=" + isLeft);

            }
        }

    }


    public static final Set<String> restartAnyTouchViewKeys = new HashSet<>(
            Arrays.asList("any_touch_view_enabled",
                    "any_touch_view_trigger_area",
                    "any_touch_view_area_width",
                    "any_touch_view_area_height",
                    "secondary_display_any_touch_view_enabled")
    );

    @Override
    public void onSharedPreferenceChanged(SharedPreferences sharedPreferences, @Nullable String key) {
        if ("any_touch_view_show_background".equals(key)) {
            AppContext.post(() -> {
                for (WindowHolder holder : windowManagerByDisplay.values()) {
                    for (AnyTouchView view : holder.anyTouchViews) {
                        view.refreshVisible();
                    }
                }
            }, 100);
        } else if (restartAnyTouchViewKeys.contains(key)) {
            AppContext.post(() -> {
                isRunning = false;
                reEnabledAccessibilityService();
            }, 100);
        }
    }

    @Override
    public void onLogData(LogData logData) {
        if (logBuffer.size() >= 500) {
            logBuffer.removeFirst();
        }
        logBuffer.add(logData);
    }

    interface GestureAction {
        void action(boolean isLeft, int displayId);
    }
}
