package io.github.netamade.display;

import android.content.Context;
import android.hardware.display.DisplayManager;
import android.view.Display;
import android.view.WindowManager;

import io.github.netamade.AppContext;
import lombok.Getter;


public class DisplayHelper {

    public static Display[] getDisplays() {
        DisplayManager displayManager = (DisplayManager) AppContext.applicationContext().getSystemService(Context.DISPLAY_SERVICE);
        return displayManager.getDisplays();
    }

    public static DisplayInfo[] getDisplayInfos() {
        Display[] displays = getDisplays();
        DisplayInfo[] displayInfos = new DisplayInfo[displays.length];
        for (int i = 0; i < displays.length; i++) {
            Context displayContext = AppContext.applicationContext().createDisplayContext(displays[i]);
            WindowManager windowManager = (WindowManager) displayContext.getSystemService(Context.WINDOW_SERVICE);
            displayInfos[i] = new DisplayInfo(displayContext, displays[i], windowManager);
        }

        return displayInfos;
    }

    @Getter
    public static class DisplayInfo {
        private final Context context;
        private final Display display;
        private final WindowManager windowsManager;

        public DisplayInfo(Context context, Display display, WindowManager windowsManager) {
            this.context = context;
            this.display = display;
            this.windowsManager = windowsManager;
        }

    }

}
