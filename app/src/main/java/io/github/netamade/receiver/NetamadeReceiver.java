package io.github.netamade.receiver;


import static android.content.Context.RECEIVER_NOT_EXPORTED;

import android.annotation.SuppressLint;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.util.Log;

import com.topjohnwu.superuser.Shell;

import io.github.netamade.AppSettings;
import io.github.netamade.GrantActivity;
import io.github.netamade.GrantOption;
import io.github.netamade.entity.MapType;
import io.github.netamade.service.AnyTouchService;
import io.github.netamade.util.HozonSettings;
import io.github.netamade.util.SystemAccessibilityUtils;

public class NetamadeReceiver extends BroadcastReceiver {
    private static final String TAG = "BootReceiver";

    @SuppressLint({"UnspecifiedRegisterReceiverFlag", "WrongConstant", "InlinedApi"})
    public static void register(Context context) {
        IntentFilter filter = new IntentFilter();
        filter.addAction(Intent.ACTION_SCREEN_ON);
        filter.addAction(HozonSettings.ACTION_LAUNCH_HOZON_MAP);

        context.registerReceiver(new NetamadeReceiver(), filter, RECEIVER_NOT_EXPORTED);

    }

    private void triggerEvent(Context context, String action, AppSettings.TriggerEventSetting setting) {
        if (setting == null || !setting.isEnabled()) {
            return;
        }

        if (setting.isBlockAISpeechNetwork() && Boolean.TRUE.equals(Shell.isAppGrantedRoot())) {
            HozonSettings.blockAISpeechDaemonNetwork();
        }

        String selectedMap = setting.getSelectedMap();

        boolean launchMap = setting.isLaunchMap() && !"none".equals(selectedMap);

        int countdown = setting.getCountdownSeconds();

        Log.i(TAG, "触发自动任务 action=" + action + " selectMap=" + selectedMap + " launchMap=" + launchMap + " countdown=" + countdown);


        MapType targetMapType = null;
        if (launchMap) {
            targetMapType = MapType.get(selectedMap);
        }
        GrantActivity.launch(context, GrantOption.builder()
                .forceGrant(true)
                .skipRevokePermission(false)
                .launchMapDelay(5)
                .targetMap(targetMapType)
                .countdownSeconds(countdown)
                .unlockSwckeyState(setting.isUnlockSwckeyState())
                .build());

    }

    @Override
    public void onReceive(Context context, Intent intent) {
        String action = intent.getAction();

        if (action == null) {
            return;
        }
        switch (action) {
            case Intent.ACTION_BOOT_COMPLETED:

                Log.i(TAG, "boot completed, enabling accessibility service");
                AnyTouchService.reEnabledAccessibilityService();


                context.startForegroundService(new Intent(context, AnyTouchService.class));

                break;
            case Intent.ACTION_SCREEN_ON:
                triggerEvent(context, action, AppSettings.getScreenOn());
                break;
            case HozonSettings.ACTION_LAUNCH_HOZON_MAP:
                if (AppSettings.isHijackSystemMapOpeningOnce()) {
                    AppSettings.setHijackSystemMapOpeningOnce(false);
                    return;
                }
                MapType targetMapType = null;
                String targetMap = AppSettings.getHijackSystemMapOpeningTargetMap();
                Log.i(TAG, "start hijacking system map => " + targetMap);
                if ("none".equals(targetMap)) {
                    return;
                }

                targetMapType = MapType.get(targetMap);

                if (targetMapType == null) {
                    return;
                }

                /**
                 * check if granted location permission
                 */
                if (!HozonSettings.isLocationSwitchChecked() || HozonSettings.getLocationAuthorizedTimeInDays() <= 0) {
                    triggerEvent(context, action, new AppSettings.TriggerEventSetting(null, null) {
                        @Override
                        public boolean isClearTask() {
                            return true;
                        }

                        @Override
                        public boolean isEnabled() {
                            return true;
                        }

                        @Override
                        public boolean isLaunchMap() {
                            return true;
                        }

                        @Override
                        public int getCountdownSeconds() {
                            return 0;
                        }

                        @Override
                        public String getSelectedMap() {
                            return targetMap;
                        }

                        @Override
                        public boolean isUnlockSwckeyState() {
                            return false;
                        }
                    });
                } else {
                    targetMapType.launch(context);
                }
                break;
            default:
                break;
        }
        SystemAccessibilityUtils.keepAccessibilityServiceAlive();
    }
}
