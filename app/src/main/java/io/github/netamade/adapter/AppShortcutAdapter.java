package io.github.netamade.adapter;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import java.util.ArrayList;
import java.util.List;

import io.github.netamade.AppSettings;
import io.github.netamade.R;
import io.github.netamade.databinding.LayoutAppShortcutItemBinding;
import io.github.netamade.entity.AppShortcut;
import io.github.netamade.util.ApkUtils;
import io.github.netamade.util.HozonSettings;


public class AppShortcutAdapter extends RecyclerView.Adapter<AppShortcutAdapter.AppsViewHolder> {
    private final List<AppShortcut> appList;
    private final Context context;
    private final OnCloseListener onClose;

    public interface OnCloseListener {
        void onClose();
    }

    public AppShortcutAdapter(Context context, ArrayList<AppShortcut> appList, OnCloseListener onClose) {
        this.appList = appList;
        this.context = context;
        this.onClose = onClose;
    }

    @NonNull
    @Override
    public AppsViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new AppsViewHolder(LayoutInflater.from(context).inflate(R.layout.layout_app_shortcut_item, parent, false));
    }

    @Override
    public void onBindViewHolder(@NonNull AppsViewHolder holder, int i) {
        AppShortcut shortcut = this.appList.get(i);

        String packageName = shortcut.getPackageName();

        PackageInfo packageInfo = null;
        try {
            packageInfo = context.getPackageManager().getPackageInfo(packageName, PackageManager.GET_ACTIVITIES);
            holder.binding.appIcon.setImageDrawable(ApkUtils.loadAppIcon(context.getPackageManager(), packageInfo, shortcut.getActivityClass()));
        } catch (PackageManager.NameNotFoundException ignored) {

        }

        holder.binding.appName.setText(shortcut.getAppName());
        holder.binding.getRoot().setOnClickListener((v -> {
            Intent intent = new Intent();
            intent.setComponent(new ComponentName(shortcut.getPackageName(), shortcut.getActivityClass()));
            if (shortcut.getPackageName().equals(context.getPackageName())) {
                intent.putExtra("force-grant", true);
            }
            if (!AppSettings.isLaunchNoClearTask()) {
                intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK);
            }
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            HozonSettings.startActivity(intent);

            if (onClose != null) {
                onClose.onClose();
            }
        }));
    }


    @Override
    public int getItemCount() {
        return this.appList.size();
    }

    public static class AppsViewHolder extends RecyclerView.ViewHolder {
        LayoutAppShortcutItemBinding binding;

        public AppsViewHolder(@NonNull View itemView) {
            super(itemView);
            this.binding = LayoutAppShortcutItemBinding.bind(itemView);
        }
    }
}
