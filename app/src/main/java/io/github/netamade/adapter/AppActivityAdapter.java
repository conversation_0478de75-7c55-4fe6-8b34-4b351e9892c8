package io.github.netamade.adapter;

import android.content.Context;
import android.content.Intent;
import android.graphics.drawable.Drawable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

import io.github.netamade.AppSettings;
import io.github.netamade.MainActivity;
import io.github.netamade.databinding.LayoutAppActivityItemBinding;
import io.github.netamade.entity.AppShortcut;
import io.github.netamade.util.CaptureRunHelper;


public class AppActivityAdapter extends RecyclerView.Adapter<AppActivityAdapter.AppsViewHolder> {
    private final List<AppShortcut> appShortcuts;
    private final Context context;

    public AppActivityAdapter(Context context, List<AppShortcut> list) {
        this.context = context;
        this.appShortcuts = list;
    }

    @NonNull
    @Override
    public AppsViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        LayoutAppActivityItemBinding binding = LayoutAppActivityItemBinding.inflate(LayoutInflater.from(context));
        return new AppsViewHolder(binding.getRoot(), binding);
    }

    @Override
    public void onBindViewHolder(@NonNull AppsViewHolder holder, int i) {
        AppShortcut shortcut = appShortcuts.get(i);
        Set<String> anyTouchAppShortCuts = AppSettings.getAnyTouchAppShortCuts();
        String shortcutString = shortcut.toString();

        holder.binding.activityLabel.setText(shortcut.getAppName());
        holder.binding.activityName.setText(shortcut.getActivityClass());

        holder.binding.showInAppShortcut.setChecked(anyTouchAppShortCuts.contains(shortcutString));
        holder.binding.showInAppShortcut.setOnCheckedChangeListener((buttonView, isChecked) -> {

            Set<String> shortCuts = new HashSet<>(AppSettings.getAnyTouchAppShortCuts());

            if (isChecked) {
                shortCuts.add(shortcutString);
            } else {
                shortCuts.remove(shortcutString);
            }

            AppSettings.setAnyTouchAppShortCuts(shortCuts);

        });

        holder.binding.launchApp.setOnClickListener(v -> {

            CaptureRunHelper.from(MainActivity.class).run(() -> {

                Intent intent = new Intent();
                intent.setClassName(shortcut.getPackageName(), shortcut.getActivityClass());
                v.getContext().startActivity(intent);


            });
        });

        Drawable activityIcon = shortcut.getActivityIcon();
        if (activityIcon != null) {
            holder.binding.activityIcon.setImageDrawable(activityIcon);
        } else {
            holder.binding.activityIcon.setImageDrawable(shortcut.loadIcon(context.getPackageManager()));
        }
        holder.binding.defaultShortcut.setVisibility(shortcut.isDefault() ? View.VISIBLE : View.GONE);

    }


    @Override
    public int getItemCount() {
        return this.appShortcuts.size();
    }

    public static class AppsViewHolder extends RecyclerView.ViewHolder {
        LayoutAppActivityItemBinding binding;

        public AppsViewHolder(@NonNull View itemView, LayoutAppActivityItemBinding binding) {
            super(itemView);
            this.binding = binding;
        }
    }
}
