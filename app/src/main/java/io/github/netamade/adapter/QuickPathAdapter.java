package io.github.netamade.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import java.util.List;

import io.github.netamade.databinding.LayoutQuickPathItemBinding;
import io.github.netamade.ui.dialog.FilePickerDialog;

public class QuickPathAdapter extends RecyclerView.Adapter<QuickPathAdapter.QuickPathViewHolder> {
    private final Context context;
    private final List<FilePickerDialog.QuickPath> quickPaths;
    private OnItemClickListener onItemClickListener;

    public interface OnItemClickListener {
        void onItemClick(FilePickerDialog.QuickPath quickPath);
    }

    public QuickPathAdapter(Context context, List<FilePickerDialog.QuickPath> quickPaths) {
        this.context = context;
        this.quickPaths = quickPaths;
    }

    public void setOnItemClickListener(OnItemClickListener listener) {
        this.onItemClickListener = listener;
    }

    @NonNull
    @Override
    public QuickPathViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        LayoutQuickPathItemBinding binding = LayoutQuickPathItemBinding.inflate(
                LayoutInflater.from(context), parent, false);
        return new QuickPathViewHolder(binding);
    }

    @Override
    public void onBindViewHolder(@NonNull QuickPathViewHolder holder, int position) {
        FilePickerDialog.QuickPath quickPath = quickPaths.get(position);
        
        holder.binding.pathName.setText(quickPath.name);
        holder.binding.pathValue.setText(quickPath.path);
        
        holder.itemView.setOnClickListener(v -> {
            if (onItemClickListener != null) {
                onItemClickListener.onItemClick(quickPath);
            }
        });
    }

    @Override
    public int getItemCount() {
        return quickPaths.size();
    }

    public static class QuickPathViewHolder extends RecyclerView.ViewHolder {
        LayoutQuickPathItemBinding binding;

        public QuickPathViewHolder(LayoutQuickPathItemBinding binding) {
            super(binding.getRoot());
            this.binding = binding;
        }
    }
}
