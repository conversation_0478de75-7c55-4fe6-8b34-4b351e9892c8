package io.github.netamade.adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import java.util.List;

import io.github.netamade.MainActivity;
import io.github.netamade.R;
import io.github.netamade.databinding.LayoutAppInfoBinding;
import io.github.netamade.entity.AppInfo;
import io.github.netamade.util.CaptureRunHelper;


public class AppsAdapter extends RecyclerView.Adapter<AppsAdapter.AppsViewHolder> {
    private final List<AppInfo> appList;
    private final Context context;

    public AppsAdapter(Context context, List<AppInfo> list) {
        this.appList = list;
        this.context = context;
    }

    @SuppressLint("NotifyDataSetChanged")
    public void updateAppList(List<AppInfo> list) {
        this.appList.clear();
        this.appList.addAll(list);
        notifyDataSetChanged();
    }

    @NonNull
    @Override
    public AppsViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new AppsViewHolder(LayoutInflater.from(context).inflate(R.layout.layout_app_info, parent, false));
    }

    @Override
    public void onBindViewHolder(@NonNull AppsViewHolder holder, int i) {
        AppInfo appInfo = this.appList.get(i);
        holder.binding.appIcon.setImageDrawable(appInfo.getIcon());
        String stringBuilder = appInfo.getVersionName() + " (" + appInfo.getVersionCode() + ")";
        holder.binding.version.setText(stringBuilder);
        holder.binding.appName.setText(appInfo.getAppName());
        holder.binding.packageName.setText(appInfo.getPackageName());
        holder.binding.uninstallApp.setOnClickListener(v -> {
            CaptureRunHelper.from(MainActivity.class).run(() -> {
                Intent intent = new Intent(Intent.ACTION_DELETE);
                intent.setData(Uri.parse("package:" + appInfo.getPackageName()));
                v.getContext().startActivity(intent);
            });
        });

        if (appInfo.hasShortcuts()) {

            holder.binding.expandIcon.setVisibility(View.VISIBLE);

            LinearLayoutManager layoutManager = new LinearLayoutManager(context);
            holder.binding.recyclerView.setLayoutManager(layoutManager);

            holder.binding.recyclerView.setAdapter(new AppActivityAdapter(context, appInfo.getShortcuts()));

            View.OnClickListener onClickListener = v -> {
                if (appInfo.isExpanded()) {

                    holder.binding.expandIcon.setImageResource(R.drawable.ic_collapsed);
                    appInfo.setExpanded(false);
                    holder.binding.recyclerView.setVisibility(View.GONE);

                } else {

                    holder.binding.expandIcon.setImageResource(R.drawable.ic_expanded);
                    appInfo.setExpanded(true);
                    holder.binding.recyclerView.setVisibility(View.VISIBLE);

                }
            };
            holder.binding.expandIcon.setOnClickListener(onClickListener);
            holder.binding.rootLayout.setOnClickListener(onClickListener);

            holder.binding.expandIcon.setImageResource(appInfo.isExpanded() ? R.drawable.ic_expanded : R.drawable.ic_collapsed);
            holder.binding.recyclerView.setVisibility(appInfo.isExpanded() ? View.VISIBLE : View.GONE);

        } else {

            holder.binding.recyclerView.setVisibility(View.GONE);
            holder.binding.expandIcon.setVisibility(View.INVISIBLE);

        }
    }


    @Override
    public int getItemCount() {
        return this.appList.size();
    }

    public static class AppsViewHolder extends RecyclerView.ViewHolder {
        LayoutAppInfoBinding binding;

        public AppsViewHolder(@NonNull View itemView) {
            super(itemView);
            this.binding = LayoutAppInfoBinding.bind(itemView);
        }
    }
}
