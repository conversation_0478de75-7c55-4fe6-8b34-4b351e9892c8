package io.github.netamade.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import java.util.List;

import io.github.netamade.R;
import io.github.netamade.databinding.LayoutAppActivityItemBinding;
import io.github.netamade.databinding.LayoutFileItemBinding;
import io.github.netamade.entity.FileItem;

public class FilePickerAdapter extends RecyclerView.Adapter<FilePickerAdapter.FileViewHolder> {
    private final Context context;
    private final List<FileItem> fileItems;
    private OnItemClickListener onItemClickListener;

    public interface OnItemClickListener {
        void onItemClick(FileItem item);
    }

    public FilePickerAdapter(Context context, List<FileItem> fileItems) {
        this.context = context;
        this.fileItems = fileItems;
    }

    public void setOnItemClickListener(OnItemClickListener listener) {
        this.onItemClickListener = listener;
    }

    @NonNull
    @Override
    public FileViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        LayoutFileItemBinding binding = LayoutFileItemBinding.inflate(LayoutInflater.from(context), parent, false);
        return new FilePickerAdapter.FileViewHolder(binding);
    }

    @Override
    public void onBindViewHolder(@NonNull FileViewHolder holder, int position) {
        FileItem item = fileItems.get(position);
        holder.binding.fileName.setText(item.getName());
        holder.binding.filePermission.setText(item.getPerm());

        if (item.isDirectory()) {
            holder.binding.icon.setImageResource(R.drawable.ic_directory);
        } else {
            holder.binding.icon.setImageResource(R.drawable.ic_file);
        }

        holder.itemView.setOnClickListener(v -> {
            if (onItemClickListener != null) {
                onItemClickListener.onItemClick(item);
            }
        });
    }

    @Override
    public int getItemCount() {
        return fileItems.size();
    }

    public static class FileViewHolder extends RecyclerView.ViewHolder {
        LayoutFileItemBinding binding;

        public FileViewHolder(@NonNull LayoutFileItemBinding binding) {
            super(binding.getRoot());
            this.binding = binding;
        }
    }
}
