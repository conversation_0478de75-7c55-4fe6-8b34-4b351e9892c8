package io.github.netamade.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import java.util.List;

import io.github.netamade.R;
import io.github.netamade.databinding.LayoutAppPickerItemBinding;
import io.github.netamade.entity.AppInfo;

public class AppPickerAdapter extends RecyclerView.Adapter<AppPickerAdapter.AppPickerViewHolder> {
    private final Context context;
    private final List<AppInfo> appInfos;
    private OnItemClickListener onItemClickListener;

    public interface OnItemClickListener {
        void onItemClick(AppInfo appInfo);
    }

    public AppPickerAdapter(Context context, List<AppInfo> appInfos) {
        this.context = context;
        this.appInfos = appInfos;
    }

    public void setOnItemClickListener(OnItemClickListener listener) {
        this.onItemClickListener = listener;
    }

    @NonNull
    @Override
    public AppPickerViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        LayoutAppPickerItemBinding binding = LayoutAppPickerItemBinding.inflate(
                LayoutInflater.from(context), parent, false);
        return new AppPickerViewHolder(binding);
    }

    @Override
    public void onBindViewHolder(@NonNull AppPickerViewHolder holder, int position) {
        AppInfo appInfo = appInfos.get(position);
        
        holder.binding.appIcon.setImageDrawable(appInfo.getIcon());
        holder.binding.appName.setText(appInfo.getAppName());
        holder.binding.packageName.setText(appInfo.getPackageName());
        
        // Show activity info if available
        if (appInfo.hasShortcuts() && !appInfo.getShortcuts().isEmpty()) {
            holder.binding.activityName.setText(appInfo.getShortcuts().get(0).getActivityClass());
            holder.binding.activityName.setVisibility(View.VISIBLE);
        } else {
            holder.binding.activityName.setVisibility(View.GONE);
        }
        
        // Show system app indicator
        if (appInfo.isSystem()) {
            holder.binding.systemIndicator.setVisibility(View.VISIBLE);
        } else {
            holder.binding.systemIndicator.setVisibility(View.GONE);
        }
        
        holder.itemView.setOnClickListener(v -> {
            if (onItemClickListener != null) {
                onItemClickListener.onItemClick(appInfo);
            }
        });
    }

    @Override
    public int getItemCount() {
        return appInfos.size();
    }

    public static class AppPickerViewHolder extends RecyclerView.ViewHolder {
        LayoutAppPickerItemBinding binding;

        public AppPickerViewHolder(LayoutAppPickerItemBinding binding) {
            super(binding.getRoot());
            this.binding = binding;
        }
    }
}
