package io.github.netamade.adapter;

import android.content.Context;
import android.content.pm.PackageManager;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import java.util.List;

import io.github.netamade.R;
import io.github.netamade.databinding.LayoutAppIconGridItemBinding;
import io.github.netamade.entity.AppIconItem;

public class AppIconGridAdapter extends RecyclerView.Adapter<AppIconGridAdapter.AppIconViewHolder> {
    private final Context context;
    private final List<AppIconItem> appIconItems;
    private OnItemClickListener onItemClickListener;
    private OnItemLongClickListener onItemLongClickListener;

    public interface OnItemClickListener {
        void onItemClick(int position);
    }

    public interface OnItemLongClickListener {
        void onItemLongClick(int position);
    }

    public AppIconGridAdapter(Context context, List<AppIconItem> appIconItems) {
        this.context = context;
        this.appIconItems = appIconItems;
    }

    public void setOnItemClickListener(OnItemClickListener listener) {
        this.onItemClickListener = listener;
    }

    public void setOnItemLongClickListener(OnItemLongClickListener listener) {
        this.onItemLongClickListener = listener;
    }

    @NonNull
    @Override
    public AppIconViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        LayoutAppIconGridItemBinding binding = LayoutAppIconGridItemBinding.inflate(
                LayoutInflater.from(context), parent, false);
        return new AppIconViewHolder(binding);
    }

    @Override
    public void onBindViewHolder(@NonNull AppIconViewHolder holder, int position) {
        AppIconItem item = appIconItems.get(position);
        
        // Set app name
        holder.binding.appName.setText(item.getDisplayName());
        holder.binding.getRoot().setBackgroundColor(Color.TRANSPARENT);
        if(item.isDock()) {
            holder.binding.appName.setText(item.getDisplayName() + " (Dock)");
            holder.binding.getRoot().setBackgroundResource(R.drawable.border);
        }
        
        // Set app icon
        Drawable icon = getAppIcon(item);
        holder.binding.appIcon.setImageDrawable(icon);
        
        // Set click listeners
        holder.itemView.setOnClickListener(v -> {
            if (onItemClickListener != null) {
                onItemClickListener.onItemClick(position);
            }
        });
        
        holder.itemView.setOnLongClickListener(v -> {
            if (onItemLongClickListener != null) {
                onItemLongClickListener.onItemLongClick(position);
            }
            return true;
        });

    }

    @Override
    public int getItemCount() {
        return appIconItems.size();
    }

    private Drawable getAppIcon(AppIconItem item) {
        // First try to use the cached drawable
        if (item.getIconDrawable() != null) {
            return item.getIconDrawable();
        }
        
        // Try to get icon from package manager
        if (item.getPackageName() != null && !item.getPackageName().isEmpty()) {
            try {
                PackageManager pm = context.getPackageManager();
                Drawable icon = pm.getApplicationIcon(item.getPackageName());
                item.setIconDrawable(icon);
                return icon;
            } catch (PackageManager.NameNotFoundException e) {
                // Package not found, fall through to default
            }
        }
        
        // Try to get icon by name from resources
        if (item.getIcon() != null && !item.getIcon().isEmpty()) {
            try {
                int resId = context.getResources().getIdentifier(
                        item.getIcon(), "drawable", context.getPackageName());
                if (resId != 0) {
                    return ContextCompat.getDrawable(context, resId);
                }
            } catch (Exception e) {
                // Resource not found, fall through to default
            }
        }
        
        // Return default icon
        return ContextCompat.getDrawable(context, android.R.drawable.sym_def_app_icon);
    }

    static class AppIconViewHolder extends RecyclerView.ViewHolder {
        LayoutAppIconGridItemBinding binding;

        public AppIconViewHolder(LayoutAppIconGridItemBinding binding) {
            super(binding.getRoot());
            this.binding = binding;
        }
    }
}
