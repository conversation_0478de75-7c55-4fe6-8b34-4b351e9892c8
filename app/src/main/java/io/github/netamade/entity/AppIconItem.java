package io.github.netamade.entity;

import android.graphics.drawable.Drawable;
import android.os.Parcel;
import android.os.Parcelable;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AppIconItem implements Parcelable {
    private String appName;
    private String className;
    private String packageName;
    private String icon;
    private boolean isUsed;
    private boolean cloudConfigFlag;
    private boolean redPointFlag;
    private boolean newAdd;
    
    // Transient fields for UI
    private transient Drawable iconDrawable;
    private transient boolean isSystemApp;
    private transient boolean isDock;
    
    public AppIconItem(String appName, String className, String packageName) {
        this.appName = appName;
        this.className = className;
        this.packageName = packageName;
        this.isUsed = true;
        this.cloudConfigFlag = true;
        this.redPointFlag = false;
        this.newAdd = false;
    }

    protected AppIconItem(Parcel in) {
        appName = in.readString();
        className = in.readString();
        packageName = in.readString();
        icon = in.readString();
        isUsed = in.readByte() != 0;
        cloudConfigFlag = in.readByte() != 0;
        redPointFlag = in.readByte() != 0;
        newAdd = in.readByte() != 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(appName);
        dest.writeString(className);
        dest.writeString(packageName);
        dest.writeString(icon);
        dest.writeByte((byte) (isUsed ? 1 : 0));
        dest.writeByte((byte) (cloudConfigFlag ? 1 : 0));
        dest.writeByte((byte) (redPointFlag ? 1 : 0));
        dest.writeByte((byte) (newAdd ? 1 : 0));
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<AppIconItem> CREATOR = new Creator<AppIconItem>() {
        @Override
        public AppIconItem createFromParcel(Parcel in) {
            return new AppIconItem(in);
        }

        @Override
        public AppIconItem[] newArray(int size) {
            return new AppIconItem[size];
        }
    };

    public String getDisplayName() {
        return appName != null && !appName.isEmpty() ? appName : packageName;
    }
    
    public String getDisplayIcon() {
        return icon != null && !icon.isEmpty() ? icon : "default_icon";
    }
}
