package io.github.netamade.entity;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Set;

import io.github.netamade.AppSettings;
import io.github.netamade.MapTypeHelper;
import io.github.netamade.util.HozonSettings;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@AllArgsConstructor
@NoArgsConstructor
public class MapType {

    public static final String TAG = MapType.class.getSimpleName();
    private static final Map<String, MapType> mapTypes = new LinkedHashMap<>();

    public static void load(Context context) {
        mapTypes.clear();
        try (InputStream is = context.getAssets().open("shortcuts.json");) {

            Map<String, MapType> data = new Gson().fromJson(new InputStreamReader(is), new TypeToken<Map<String, MapType>>() {
            }.getType());

            data.forEach((k, v) -> {
                Log.i(TAG, "load map types: " + v.getKey() + " => " + v.getLaunchActivity());
            });

            mapTypes.putAll(data);

        } catch (Exception e) {
            Log.e(TAG, "failed to load shortcuts.json: " + e.getMessage());
            e.printStackTrace();
        }
    }

    public static MapType get(String key) {
        return mapTypes.get(key);
    }

    public static Collection<MapType> values() {
        return mapTypes.values();
    }

    private String key;
    private String name;
    private String packageName;
    private String icon;
    private String launchActivity;
    private String intent;
    private Set<String> activities;


    public void launch(Context context) {
        Intent intent = new Intent();
        intent.setAction(Intent.ACTION_MAIN);
        intent.addCategory(Intent.CATEGORY_LAUNCHER);

        String lastActivity = MapTypeHelper.getLastActivity(packageName);
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);

        if (lastActivity == null) {
            if (!AppSettings.isLaunchNoClearTask()) {
                intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK);
            }
            //        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
            intent.setComponent(new ComponentName(packageName, launchActivity));
        } else {
            intent.setComponent(new ComponentName(packageName, lastActivity));
        }
        try {
            HozonSettings.startActivity(context, intent);
        } catch (Throwable ignored) {
            // 失败了就不尝试记录的activity了
            intent.setComponent(new ComponentName(packageName, launchActivity));
            if (!AppSettings.isLaunchNoClearTask()) {
                intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK);
            }
            HozonSettings.startActivity(context, intent);
        }
    }
}
