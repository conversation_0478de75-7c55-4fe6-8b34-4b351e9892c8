package io.github.netamade.entity;

import android.content.pm.ActivityInfo;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.graphics.drawable.Drawable;

import androidx.annotation.NonNull;

import org.json.JSONObject;

import java.util.Map;

import lombok.Data;


@Data
public class AppShortcut {
    private final String appName;
    private final String packageName;
    private final String activityClass;
    private Drawable activityIcon;
    private boolean isDefault;

    private transient String shortcutString;

    public AppShortcut(String appName, String packageName, String activityClass) {
        this.appName = appName;
        this.packageName = packageName;
        this.activityClass = activityClass;
    }

    public Drawable loadIcon(PackageManager pm) {
        try {

            PackageInfo packageInfo = pm.getPackageInfo(packageName, PackageManager.GET_ACTIVITIES);

            for (ActivityInfo activity : packageInfo.activities) {

                if (activity.name.equals(activityClass)) {

                    Drawable drawable = activity.loadIcon(pm);

                    this.activityIcon = drawable;
                    return drawable;

                }

            }

        } catch (PackageManager.NameNotFoundException ignored) {

        }
        return null;
    }

    @NonNull
    @Override
    public String toString() {
        if (shortcutString == null) {
            shortcutString = new JSONObject(Map.of("a", appName, "b", packageName, "c", activityClass)).toString();
        }
        return shortcutString;
    }

    public static AppShortcut from(String s) {
        try {
            JSONObject jsonObject = new JSONObject(s);
            String appName = jsonObject.getString("a");
            String packageName = jsonObject.getString("b");
            String activityClass = jsonObject.getString("c");

            return new AppShortcut(appName, packageName, activityClass);

        } catch (Throwable e) {
            e.printStackTrace();
            return null;
        }
    }

}
