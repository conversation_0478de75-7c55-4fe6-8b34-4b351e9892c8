package io.github.netamade.entity;

import android.graphics.drawable.Drawable;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AccessibilityStatusInfo {

    private String key;
    private String title;
    private String summary;
    private String settingsActivityName;
    private String packageName;
    private String serviceName;
    private String serviceId;
    private boolean keepEnabled;
    private Status status;
    private transient Drawable appIcon;

    public enum Status {
        DISABLED,
        ENABLED,
        KEEP_ENABLED,
    }
}
