package io.github.netamade.entity;

import android.content.pm.ActivityInfo;
import android.graphics.Bitmap;
import android.graphics.drawable.Drawable;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AppInfo {
    private String apkPlacement;
    private long apkSize;
    private String appName;
    private long firstInstallTime;
    private Drawable icon;
    private boolean isFrozen = false;
    private boolean isSystem = false;
    private long lastUpdateTime;
    private int minSdkVersion;
    private String packageName;
    private int targetSdkVersion;
    private long versionCode;
    private String versionName;
    private ActivityInfo[] activities;

    private List<AppShortcut> shortcuts;

    private transient boolean expanded;
    private transient boolean shortcutAdded;

    public boolean hasShortcuts() {
        return shortcuts != null && !shortcuts.isEmpty();
    }

}
