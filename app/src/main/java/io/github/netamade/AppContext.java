package io.github.netamade;

import android.app.Activity;
import android.app.Application;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.topjohnwu.superuser.Shell;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.ref.WeakReference;

import cat.ereza.customactivityoncrash.config.CaocConfig;
import io.github.netamade.entity.MapType;
import io.github.netamade.exemptions.Exemptions;
import io.github.netamade.receiver.NetamadeReceiver;
import io.github.netamade.service.AnyTouchService;
import io.github.netamade.util.CaptureRunHelper;

public class AppContext extends Application implements Application.ActivityLifecycleCallbacks {

    public static final String TAG = AppContext.class.getSimpleName();
    private static WeakReference<Context> sApplicationContext;
    private static Handler sHandler;
    public static final String BACK_PNG = "/sdcard/tmp/.back.png";

    public static final String PACKAGE_NAME = "io.github.netamade";

    public static Context applicationContext() {
        return sApplicationContext.get();
    }

    public static Handler mainHandler() {
        return sHandler;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        this.registerActivityLifecycleCallbacks(this);
        sApplicationContext = new WeakReference<>(this);

        sHandler = new Handler(Looper.getMainLooper());
        initExceptionHandler();
        Exemptions.hack();
        AppSettings.init(this);
        MapType.load(this);

        NetamadeReceiver.register(this);

        AnyTouchService.reEnabledAccessibilityService();

        Intent service = new Intent(this, AnyTouchService.class);
        stopService(service);
        startForegroundService(service);


    }
    static {
        // Set settings before the main shell can be created
        Shell.enableVerboseLogging = true;
        Shell.setDefaultBuilder(Shell.Builder.create()
                .setFlags(Shell.FLAG_MOUNT_MASTER)
                .setTimeout(10));
    }
    private void initExceptionHandler() {
        CaocConfig.Builder.create()
                .backgroundMode(CaocConfig.BACKGROUND_MODE_SILENT)
                .enabled(true)
                .showErrorDetails(true)
                .showRestartButton(true)
                .logErrorOnRestart(false)
                .trackActivities(true)
                .minTimeBetweenCrashesMs(2000)
                .errorDrawable(R.drawable.ic_debug)
                .restartActivity(MainActivity.class)
                .apply();
    }

    public static void post(Runnable runnable) {
        if (sHandler != null && runnable != null)
            sHandler.post(runnable);
    }

    public static void post(Runnable runnable, long delay) {
        if (sHandler != null && runnable != null)
            sHandler.postDelayed(runnable, delay);
    }

    @Override
    public void onActivityCreated(@NonNull Activity activity, @Nullable Bundle savedInstanceState) {
        CaptureRunHelper.register(activity);
    }

    @Override
    public void onActivityDestroyed(@NonNull Activity activity) {
        CaptureRunHelper.unregister(activity);
    }

    @Override
    public void onActivityStarted(@NonNull Activity activity) {

    }

    @Override
    public void onActivityResumed(@NonNull Activity activity) {

    }

    @Override
    public void onActivityPaused(@NonNull Activity activity) {

    }

    @Override
    public void onActivityStopped(@NonNull Activity activity) {

    }

    @Override
    public void onActivitySaveInstanceState(@NonNull Activity activity, @NonNull Bundle outState) {

    }

    public static void saveImage(Context context, String assetFileName, String targetPath) throws IOException {
        // 确保目标目录存在
        File targetFile = new File(targetPath);
        File parentDir = targetFile.getParentFile();
        if (parentDir != null && !parentDir.exists()) {
            parentDir.mkdirs();
        }

        try (InputStream inputStream = context.getAssets().open(assetFileName);
             FileOutputStream outputStream = new FileOutputStream(targetFile)) {
            byte[] buffer = new byte[1024];
            int length;
            while ((length = inputStream.read(buffer)) > 0) {
                outputStream.write(buffer, 0, length);
            }
        }
    }
}
