package io.github.netamade.steps;

import android.os.Handler;

import java.util.concurrent.TimeUnit;

import io.github.netamade.util.SuspendableCountDownTimer;

public class CountdownStep extends WorkflowStep {

    private final int seconds;
    private final String fmt;
    private final int msg;
    private SuspendableCountDownTimer countDownTimer;

    public CountdownStep(String fmt, Handler handler, int seconds, int msg) {
        super(String.format(fmt, seconds), handler);

        this.msg = msg;
        this.seconds = seconds;
        this.fmt = fmt;

    }

    @Override
    protected void execute() {
        if (seconds <= 0) {
            sendMessage(msg);
            return;
        }

        this.countDownTimer = new SuspendableCountDownTimer(TimeUnit.SECONDS.toMillis(seconds), 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
                updateTitle(String.format(fmt, TimeUnit.MILLISECONDS.toSeconds(millisUntilFinished)), true);
            }

            @Override
            public void onFinish() {
                sendMessage(msg);
            }
        };

        countDownTimer.start();
    }

    public void resume() {
        if (countDownTimer != null) {
            countDownTimer.setSuspend(false);
        }
    }

    public void suspend() {
        if (countDownTimer != null) {
            countDownTimer.setSuspend(true);
        }
    }
}
