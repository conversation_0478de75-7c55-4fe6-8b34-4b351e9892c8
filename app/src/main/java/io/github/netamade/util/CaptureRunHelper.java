package io.github.netamade.util;

import android.app.Activity;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AlertDialog;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.github.netamade.ui.dialog.AutoCloseableErrorDialog;

public class CaptureRunHelper implements Handler.Callback {
    public static final String TAG = CaptureRunHelper.class.getSimpleName();

    public interface Callback {
        void execute();

        void onFinish(Throwable e);

    }

    public static void register(Activity activity) {
        Log.i(TAG, "register " + activity.getClass().getName());
        instanceMap.computeIfAbsent(activity.getClass(), x -> new CaptureRunHelper(activity));
    }

    public static void unregister(Activity activity) {
        if (activity != null) {
            Log.i(TAG, "unregister " + activity.getClass().getName());
            CaptureRunHelper helper = instanceMap.remove(activity.getClass());
            if (helper != null) {
                helper.destroy();
            }
        }
    }


    public static void run(Activity activity, Runnable runnable) {
        run(activity.getClass(), runnable);
    }

    public static void run(Activity activity, Callback callback) {
        run(activity.getClass(), callback);
    }

    public static void run(Class<? extends Activity> cls, Runnable runnable) {

        CaptureRunHelper helper = from(cls);
        if (helper == null) {
            return;
        }

        helper.run(runnable);
    }

    public static void run(Class<? extends Activity> cls, Callback callback) {

        CaptureRunHelper helper = from(cls);
        if (helper == null || callback == null) {
            return;
        }

        helper.runWithCallback(callback);
    }

    public static CaptureRunHelper from(Class<? extends Activity> cls) {
        return instanceMap.get(cls);
    }

    public static final int MSG_FINISHED = 8000;
    public static final int MSG_FAILED = MSG_FINISHED + 1;
    private static final Map<Class<? extends Activity>, CaptureRunHelper> instanceMap = new HashMap<>();

    private final Activity activity;
    private final Handler handler;

    private final List<WeakReference<AlertDialog>> showingDialogs;
    private transient Callback callback;


    private CaptureRunHelper(Activity activity) {
        this.activity = activity;
        this.showingDialogs = new ArrayList<>();
        this.handler = new Handler(Looper.getMainLooper(), this);
    }

    public void runWithCallback(Callback callback) {
        if (this.callback != null) {
            return;
        }
        try {
            this.callback = callback;
            callback.execute();
            if (handler != null) {
                handler.sendEmptyMessage(MSG_FINISHED);
            }
        } catch (Throwable e) {
            if (handler != null) {
                handler.sendMessage(Message.obtain(handler, MSG_FAILED, e));
            }
        }
    }

    private void destroy() {
        handler.removeCallbacksAndMessages(null);
        for (WeakReference<AlertDialog> ref : showingDialogs) {
            AlertDialog dialog = ref.get();
            if (dialog != null) {
                dialog.dismiss();
            }
        }
    }

    public void run(Runnable runnable) {
        runWithCallback(new Callback() {
            @Override
            public void execute() {
                runnable.run();
            }

            @Override
            public void onFinish(Throwable e) {

            }
        });
    }

    @Override
    public boolean handleMessage(@NonNull Message msg) {
        if (!instanceMap.containsKey(activity.getClass())) {
            return false;
        }
        switch (msg.what) {
            case MSG_FINISHED:
                if (this.callback != null) {
                    this.callback.onFinish(null);
                }
                Object obj = msg.obj;

                if (obj instanceof WeakReference) {
                    showingDialogs.remove(obj);
                }

                this.callback = null;
                break;
            case MSG_FAILED:
                Throwable e = (Throwable) msg.obj;

                Message closeMsg = new Message();
                closeMsg.what = MSG_FINISHED;
                closeMsg.setTarget(handler);

                AlertDialog dialog = new AutoCloseableErrorDialog(activity, 10, autoClose -> {
                    closeMsg.sendToTarget();
                }).setContent(ThrowableUtils.toString(e)).show();
                WeakReference<AlertDialog> dialogRef = new WeakReference<>(dialog);
                closeMsg.obj = dialogRef;
                showingDialogs.add(dialogRef);

                break;
        }
        return true;
    }

}
