package io.github.netamade.util;

import android.annotation.SuppressLint;
import android.debug.AdbManager;
import android.debug.IAdbManager;
import android.util.Log;

import java.util.concurrent.atomic.AtomicReference;

import io.github.netamade.AppContext;

public class AdbUtils {
    public static final String TAG = AdbUtils.class.getSimpleName();

    interface AdbProxy {
        boolean available();

        Object getAdbManager();

        boolean isAdbWifiQrSupported();

        boolean isAdbWifiSupported();

        int getAdbWirelessPort();
    }

    private static AdbProxy adbProxy = new AdbProxy() {
        @Override
        public boolean available() {
            return false;
        }

        @Override
        public Object getAdbManager() {
            return null;
        }

        @Override
        public boolean isAdbWifiQrSupported() {
            return true;
        }

        @Override
        public boolean isAdbWifiSupported() {
            return true;
        }

        @Override
        public int getAdbWirelessPort() {
            return -999;
        }
    };

    static {
        try {

            // 判断是否是在车机系统中
            Class.forName("android.debug.AdbManager");

            adbProxy = new AdbProxy() {

                private AtomicReference<IAdbManager> adbStub = null;

                @SuppressLint("WrongConstant")
                private IAdbManager get() {
                    if (adbStub == null) {
                        adbStub = new AtomicReference<>();
                        try {
                            AdbManager adb = (AdbManager) AppContext.applicationContext().getApplicationContext().getSystemService("adb");

                            if (adb == null) {
                                Log.e(TAG, "failed to get adb service (AdbManager)");
                            } else {
                                Log.i(TAG, "success to get adb service (AdbManager)");
                                IAdbManager stub = (IAdbManager) ReflectUtils.getField(adb, "mService");

                                if (stub == null) {

                                    Log.e(TAG, "failed to get stubbed adb service (IAdbManager)");

                                } else {

                                    Log.i(TAG, "success to get stubbed adb service (IAdbManager)");
                                    adbStub.set(stub);

                                }
                            }
                        } catch (Throwable e) {
                            Log.e(TAG, "failed to get IAdbManager");
                        }
                    }
                    return adbStub.get();
                }

                @Override
                public boolean available() {
                    return get() != null;
                }

                @Override
                public Object getAdbManager() {
                    return get();
                }

                @Override
                public boolean isAdbWifiQrSupported() {
                    IAdbManager iAdbManager = get();
                    if (iAdbManager == null) {
                        return false;
                    }
                    try {
                        return iAdbManager.isAdbWifiQrSupported();
                    } catch (Throwable e) {
                        String msg = ThrowableUtils.toString(e);
                        Log.i(TAG, "failed to call isAdbWifiQrSupported: " + msg);
                        return false;
                    }
                }

                @Override
                public boolean isAdbWifiSupported() {
                    IAdbManager iAdbManager = get();
                    if (iAdbManager == null) {
                        return false;
                    }
                    try {
                        return iAdbManager.isAdbWifiSupported();
                    } catch (Throwable e) {
                        String msg = ThrowableUtils.toString(e);
                        Log.i(TAG, "failed to call isAdbWifiQrSupported: " + msg);
                        return false;
                    }
                }

                @Override
                public int getAdbWirelessPort() {
                    IAdbManager iAdbManager = get();
                    if (iAdbManager != null) {
                        try {
                            return iAdbManager.getAdbWirelessPort();
                        } catch (Throwable e) {
                            String msg = ThrowableUtils.toString(e);
                            Log.i(TAG, "failed to call isAdbWifiQrSupported: " + msg);
                        }
                    }
                    return -999;
                }
            };

        } catch (Throwable e) {
            Log.e(TAG, "failed to get HozonPermissionManager");
        }
    }
}
