package io.github.netamade.util;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.provider.Settings;
import android.util.Log;

import com.topjohnwu.superuser.Shell;

import java.util.concurrent.TimeUnit;

import io.github.netamade.AppContext;
import io.github.netamade.AppSettings;

public class HozonSettings {
    public static final String TAG = HozonSettings.class.getSimpleName();
    public static final String SWITCH_PERMISSION_LOCATION_CHECKED = "SWITCH_PERMISSION_LOCATION_CHECKED";
    public static final String SENSOR_LOCATION_AUTHORIZED_TIME = "SENSOR_LOCATION_AUTHORIZED_TIME";
    public static final String ACTION_LAUNCH_HOZON_MAP = "io.github.netamade.LAUNCH_HOZON_MAP";
    public static final String ACTION_UPDATE_SWCKEY_STATE = "io.github.netamade.UPDATE_SWCKEY_STATE";
    public static final String HOZON_FACTORY_MODE_PACKAGE = "com.pateo.factorymode";
    public static final String HOZON_FACTORY_HARD_KEY_ACTIVITY = "com.pateo.factorymode.activity.HardKeyTest";
    public static final String HOZON_FACTORY_LOCK_BUTTON_ID = "com.pateo.factorymode:id/btn_close_key_test_mode";
    public static final String HOZON_FACTORY_UNLOCK_BUTTON_ID = "com.pateo.factorymode:id/btn_open_key_test_mode";
    public static final String HOZON_FACTORY_BACK_BUTTON_ID = "com.pateo.factorymode:id/bt_back";

    public static final String HOZON_MAP_PACKAGE = "com.hozonauto.launcher";
    public static final String HOZON_MAP_ACTIVITY = "com.hozonauto.navlbs.home.mvvm.ui.activity.MainHomeActivity";

    public static final String HOZON_TOYBOX_PACKAGE = "com.hozonauto.toybox";
    public static final String HOZON_TOYBOX_LOUD_VOICE_ACTIVITY = "com.hozonauto.toybox.activity.LoudVoiceActivity";
    public static final String HOZON_TOYBOX_MAIN_ACTIVITY = "com.hozonauto.toybox.MainActivity";
    public static final String ACTION_UPDATE_TOYBOX_LOUD_VOICE = "io.github.netamade.UPDATE_TOYBOX_LOUD_VOICE";
    public static final String HOZON_TOYBOX_MAIN_LOUD_VOICE_BUTTON_ID = "com.hozonauto.toybox:id/title";
    public static final String HOZON_TOYBOX_LOUD_VOICE_SPEAKER_BUTTON_ID = "com.hozonauto.toybox:id/externalSpeakerLy";


    public static final String NETA_L = "ep32";
    public static final int NETA_L_STATUS_BAR_HEIGHT = 60;

    private static final int SWC_KEY_DISABLE = 1;
    private static final int SWC_KEY_ENABLE = 0;
    private static final int SWC_KEY_FACTORY = 3;
    private static final int SWC_KEY_ONLYMUTE = 2;

    /**
     * 启动 Intent
     */
    public static void startActivity(String packageName, String activityClass) {

        Intent intent = new Intent();
        intent.setAction(Intent.ACTION_MAIN);
        intent.addCategory(Intent.CATEGORY_LAUNCHER);
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        if (!AppSettings.isLaunchNoClearTask()) {
            intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK);
        }
        intent.setComponent(new ComponentName(packageName, activityClass));

        startActivity(intent);

    }

    public static void startActivity(Context context, Intent intent) {
        setHozonSystemUIStatusBarState(false);
        context.startActivity(intent);
    }

    public static void startActivity(Intent intent) {
        setHozonSystemUIStatusBarState(false);
        AppContext.applicationContext().startActivity(intent);
    }

    /**
     * 设置状态栏状态
     */
    public static void setHozonSystemUIStatusBarState(boolean isShow) {
        Log.i(TAG, "设置状态栏状态: " + isShow);
        try {
            Intent intent = new Intent("com.hozonauto.netahome.allapp.status");
            intent.putExtra("status", isShow);
            AppContext.applicationContext().sendBroadcast(intent);
        } catch (Throwable e) {
            Log.e(TAG, "设置状态栏状态: " + e.getMessage(), e);
        }
    }

    /**
     * @return 当前定位开关是否打开
     */
    public static boolean isLocationSwitchChecked() {
        try {
            return Settings.Global.getInt(AppContext.applicationContext().getContentResolver(), SWITCH_PERMISSION_LOCATION_CHECKED, 0) == 1;
        } catch (Throwable e) {
            Log.e(TAG, "获取定位开关失败，返回 false", e);
            return false;
        }
    }

    /**
     * 设置方控状态
     */
    public static void setSWCKeyState(boolean checked) {
        Log.i(TAG, "send broadcast SWCKEY_STATE " + checked);
        try {
            Intent intent = new Intent("hz.intent.action.carsignal.SWCKEY_STATE");
            intent.putExtra("SWCKEY_STATE", checked ? 3 : 0);
            AppContext.applicationContext().sendBroadcast(intent);
        } catch (Throwable e) {
            Log.e(TAG, "设置方控失败: " + e.getMessage(), e);
        }
    }

    /**
     * 设置定位开关
     */
    public static boolean setLocationSwitchChecked(boolean checked) {
        try {
            int val = checked ? 1 : 0;
            return Settings.Global.putInt(AppContext.applicationContext().getContentResolver(), SWITCH_PERMISSION_LOCATION_CHECKED, val);
        } catch (Throwable e) {
            Log.e(TAG, "修改定位开关失败，返回 false", e);
            return false;
        }
    }

    /**
     * @return 获取定位授权结束时间
     */
    public static long getLocationAuthorizedTime() {
        try {
            return Settings.Global.getLong(AppContext.applicationContext().getContentResolver(), SENSOR_LOCATION_AUTHORIZED_TIME, 0);
        } catch (Throwable e) {
            Log.e(TAG, "获取定位授权结束时间失败，返回 0", e);
            return 0L;
        }
    }

    /**
     * @return 获取定位授权结束时间还有多少天
     */
    public static int getLocationAuthorizedTimeInDays() {
        long millis = getLocationAuthorizedTime();
        if (millis > 0) {
            long days = TimeUnit.MILLISECONDS.toDays(millis) - TimeUnit.MILLISECONDS.toDays(System.currentTimeMillis());
            if (days <= 0) {
                return 0;
            }
            return (int) days;
        }
        return 0;
    }

    /**
     * @return 设置定位授权天数
     */
    public static boolean setLocationAuthorizedTimeInDays(int days) {
        long authorizedTime = TimeUnit.DAYS.toMillis(days) + System.currentTimeMillis();
        try {
            return Settings.Global.putLong(AppContext.applicationContext().getContentResolver(), SENSOR_LOCATION_AUTHORIZED_TIME, authorizedTime);
        } catch (Throwable e) {
            Log.e(TAG, "设置定位授权天数失败", e);
            return false;
        }

    }

    /**
     *
     */

    public static final String AI_SPEECH_DAEMON_PACKAGE = "com.aispeech.lyra.daemon";
    private static String AI_SPEECH_DAEMON_UID = null;


    /**
     * iptables -D OUTPUT -m owner --uid-owner "$uid" -j REJECT 2>/dev/null
     * ip6tables -D OUTPUT -m owner --uid-owner "$uid" -j REJECT 2>/dev/null
     */

    public static void blockAISpeechDaemonNetwork() {
        String uid = getAISpeechDaemonUid();
        if (uid == null) {
            return;
        }
        Log.i(TAG, "block AISpeechDaemonNetwork: " + uid);
        execAndLog("iptables -I OUTPUT -m owner --uid-owner " + uid + " -j REJECT");
        execAndLog("ip6tables -I OUTPUT -m owner --uid-owner " + uid + " -j REJECT");
    }

    private static String execAndLog(String cmd) {
        Shell.Result result = Shell.cmd(cmd).exec();
        Log.i(TAG, "exec: " + cmd + " " + result.getOut() + " " + result.getErr());
        StringBuilder sb = new StringBuilder();
        for (String s : result.getOut()) {
            sb.append(s).append("\n");
        }
        return sb.toString();
    }


    public static String enableAdbOverWifi(){
        execAndLog("setprop persist.adb.tls_server.enable 1");
        return execAndLog("getprop service.adb.tls.port");
    }

    public static void unblockAISpeechDaemonNetwork() {
        String uid = getAISpeechDaemonUid();
        if (uid == null) {
            return;
        }
        Log.i(TAG, "unblock AISpeechDaemonNetwork: " + uid);
        execAndLog("iptables -D OUTPUT -m owner --uid-owner " + uid + " -j REJECT");
        execAndLog("ip6tables -D OUTPUT -m owner --uid-owner " + uid + " -j REJECT");
    }

    private static String getAISpeechDaemonUid() {
        if (AI_SPEECH_DAEMON_UID != null) {
            return AI_SPEECH_DAEMON_UID;
        }
        synchronized (HozonSettings.class) {
            if (AI_SPEECH_DAEMON_UID != null) {
                return AI_SPEECH_DAEMON_UID;
            }
            Shell.Result result = Shell.cmd("cat /data/system/packages.list").exec();
            for (String line : result.getOut()) {
                if (line.contains(AI_SPEECH_DAEMON_PACKAGE)) {
                    AI_SPEECH_DAEMON_UID = line.split(" ")[1];
                }
            }
        }
        Log.i(TAG, "AI_SPEECH_DAEMON_UID: " + AI_SPEECH_DAEMON_PACKAGE);
        return AI_SPEECH_DAEMON_UID;
    }

}
