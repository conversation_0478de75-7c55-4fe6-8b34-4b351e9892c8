package io.github.netamade.util;

import android.util.Log;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;

import java.util.List;

public class JsonUtils {

    public static final String TAG = JsonUtils.class.getSimpleName();
    private static final Gson gson = new GsonBuilder().serializeNulls().create();

    public static String toJson(Object obj) {
        try {
            return gson.toJson(obj);
        } catch (Exception e) {
            Log.e(TAG, "failed to marshall json: " + obj, e);
        }
        return "";
    }

    /**
     * 序列化对象
     *
     * @param type 序列化类型
     * @param def  默认值
     */
    public static <T> T parse(String json, TypeToken<T> type, T def) {
        if (json == null || json.isEmpty()) {
            return def;
        }
        try {
            T val = gson.fromJson(json, new TypeToken<T>() {
            }.getType());
            return val;
        } catch (Exception e) {
            Log.e(TAG, "failed to parse json: " + json, e);
        }
        return def;
    }

    /**
     * 序列化列表
     *
     * @param def 默认值
     */
    public static <T> List<T> parse(String json, List<T> def) {
        if (json == null || json.isEmpty()) {
            return def;
        }
        try {
            List<T> val = gson.fromJson(json, new TypeToken<List<T>>() {
            }.getType());
            return val;
        } catch (Exception e) {
            Log.e(TAG, "failed to parse json: " + json, e);
        }
        return def;
    }
}
