package io.github.netamade.util;

import android.util.Log;

import java.io.BufferedReader;
import java.io.Closeable;
import java.io.DataOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class ShellUtils {

    private static String SHELL = "sh";
    private static String ROOT_SHELL = "su";

    public static void setShell(String shell) {
        ShellUtils.SHELL = shell;
    }

    public static void setRootShell(String rootShell) {
        ROOT_SHELL = rootShell;
    }

    private static int exec(final String sh, final List<String> cmds, final Result result) {
        Process process;
        DataOutputStream stdin = null;
        OutputReader stdout = null;
        OutputReader stderr = null;
        int resultCode = -1;
        try {
            process = Runtime.getRuntime().exec(sh);
            stdin = new DataOutputStream(process.getOutputStream());
            if (result != null) {
                stdout = new OutputReader(new BufferedReader(new InputStreamReader(process.getInputStream())),
                        new Output() {
                            @Override
                            public void output(String text) {
                                result.onStdout(text);
                            }
                        });
                stderr = new OutputReader(new BufferedReader(new InputStreamReader(process.getErrorStream())),
                        new Output() {
                            @Override
                            public void output(String text) {
                                result.onStderr(text);
                            }
                        });
                stdout.start();
                stderr.start();
            }
            for (String cmd : cmds) {
                if (result != null) {
                    result.onCommand(cmd);
                }
                stdin.write(cmd.getBytes());
                stdin.writeBytes("\n");
                stdin.flush();
            }
            stdin.writeBytes("exit $?\n");
            stdin.flush();
            resultCode = process.waitFor();
            if (result != null) {
                result.onFinish(resultCode);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            safeCancel(stderr);
            safeCancel(stdout);
            safeClose(stdout);
            safeClose(stderr);
            safeClose(stdin);
        }
        return resultCode;
    }
    private static int execRoot(final List<String> cmds, final Result result) {
        Process process;
        DataOutputStream stdin = null;
        OutputReader stdout = null;
        OutputReader stderr = null;
        int resultCode = -1;
        try {
            process = Runtime.getRuntime().exec(new String[]{"su", "-", "root"});
            stdin = new DataOutputStream(process.getOutputStream());
            if (result != null) {
                stdout = new OutputReader(new BufferedReader(new InputStreamReader(process.getInputStream())),
                        new Output() {
                            @Override
                            public void output(String text) {
                                result.onStdout(text);
                            }
                        });
                stderr = new OutputReader(new BufferedReader(new InputStreamReader(process.getErrorStream())),
                        new Output() {
                            @Override
                            public void output(String text) {
                                result.onStderr(text);
                            }
                        });
                stdout.start();
                stderr.start();
            }
            for (String cmd : cmds) {
                if (result != null) {
                    result.onCommand(cmd);
                }
                stdin.write(cmd.getBytes());
                stdin.writeBytes("\n");
                stdin.flush();
            }
            stdin.writeBytes("exit $?\n");
            stdin.flush();
            resultCode = process.waitFor();
            if (result != null) {
                result.onFinish(resultCode);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            safeCancel(stderr);
            safeCancel(stdout);
            safeClose(stdout);
            safeClose(stderr);
            safeClose(stdin);
        }
        return resultCode;
    }

    private static void safeCancel(OutputReader reader) {
        try {
            if (reader != null) reader.cancel();
        } catch (Exception ignored) {

        }
    }

    private static void safeClose(Closeable closeable) {
        try {
            if (closeable != null) closeable.close();
        } catch (Exception ignored) {

        }
    }

    public static int exec(final List<String> cmds, final Result result, final boolean isRoot) {
        String sh = isRoot ? ROOT_SHELL : SHELL;
        return exec(sh, cmds, result);
    }

    public static List<String> execRootWithOutput(String... cmds) {
        List<String> output = new ArrayList<>();
        ShellUtils.execRoot(Arrays.asList(cmds), new Result() {
            @Override
            public void onStdout(String text) {
                output.add(text);
            }

            @Override
            public void onStderr(String text) {
                output.add(text);
            }

            @Override
            public void onCommand(String command) {

            }

            @Override
            public void onFinish(int resultCode) {

            }
        });
        return output;
    }


    public static int exec(final List<String> cmds, final boolean isRoot) {
        return exec(cmds, null, isRoot);
    }

    public static int exec(final String cmd, boolean isRoot) {
        return exec(cmd, null, isRoot);
    }

    public static int exec(final String cmd, final Result result, boolean isRoot) {
        List<String> cmds = new ArrayList<String>();
        cmds.add(cmd);
        return exec(cmds, result, isRoot);
    }

    public static int exec(final String cmd) {
        return exec(cmd, null, false);
    }

    public static int execWithRoot(final String cmd) {
        return exec(cmd, null, true);
    }

    public static int execWithRoot(final String cmd, final Result result) {
        return exec(cmd, result, true);
    }

    public interface Result {
        void onStdout(String text);

        void onStderr(String text);

        void onCommand(String command);

        void onFinish(int resultCode);
    }

    private interface Output {
        void output(String text);
    }

    public static class OutputReader extends Thread implements Closeable {
        private Output output = null;
        private BufferedReader reader = null;
        private boolean isRunning = false;

        private OutputReader(BufferedReader reader, Output output) {
            this.output = output;
            this.reader = reader;
            this.isRunning = true;
        }

        @Override
        public void close() {
            try {
                reader.close();
            } catch (IOException ignored) {
            }
        }

        @Override
        public void run() {
            super.run();
            String line;
            while (isRunning) {
                try {
                    line = reader.readLine();
                    if (line != null)
                        output.output(line);
                } catch (IOException ignored) {
                }
            }
        }

        private void cancel() {
            synchronized (this) {
                isRunning = false;
                this.notifyAll();
            }
        }
    }
}