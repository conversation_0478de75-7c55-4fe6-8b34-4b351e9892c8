package io.github.netamade.util;

import android.util.Log;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.topjohnwu.superuser.Shell;

import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import java.io.File;
import java.io.IOException;
import java.lang.reflect.Type;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.regex.Pattern;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;

import io.github.netamade.entity.AppIconItem;
import io.github.netamade.entity.LayoutData;

public class SharedPrefsXmlParser {
    private static final String TAG = "SharedPrefsXmlParser";
    private static final Gson gson = new Gson();
    private static final Type APP_ICON_LIST_TYPE = new TypeToken<List<AppIconItem>>() {
    }.getType();

    // Pattern for vice launcher app data keys
    private static final Pattern VICE_LAUNCHER_APP_PATTERN = Pattern.compile("ep\\d+_vicelauncher_allapp_data_[\\d.]+");
    private static final Pattern VICE_LAUNCHER_DOCK_PATTERN = Pattern.compile("vicelauncher_ep\\d+_dock_app_data_[\\d.]+");

    public static LayoutData parseXmlFile(File xmlFile) throws Exception {
        // Copy to temp file first for safety
        File tempFile = copyToTempFile(xmlFile);

        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
        DocumentBuilder builder = factory.newDocumentBuilder();
        Document document = builder.parse(tempFile);

        LayoutData layoutData = new LayoutData();
        layoutData.setOriginalFileName(xmlFile.getName());

        NodeList stringNodes = document.getElementsByTagName("string");

        for (int i = 0; i < stringNodes.getLength(); i++) {
            Node node = stringNodes.item(i);
            if (node.getNodeType() == Node.ELEMENT_NODE) {
                Element element = (Element) node;
                String name = element.getAttribute("name");
                String content = element.getTextContent();

                if ("applist.data".equals(name)) {
                    layoutData.setAppListData(parseAppIconList(content));
                } else if ("dock.data".equals(name)) {
                    List<AppIconItem> dockData = parseAppIconList(content);
                    layoutData.setDockData(dockData);
                    if (dockData != null) {
                        dockData.forEach(d -> {
                            d.setDock(true);
                        });
                    }
                } else if (VICE_LAUNCHER_APP_PATTERN.matcher(name).matches()) {
                    layoutData.setAppListData(parseAppIconList(content));
                    layoutData.setAppListKey(name);
                } else if (VICE_LAUNCHER_DOCK_PATTERN.matcher(name).matches()) {
                    List<AppIconItem> dockData = parseAppIconList(content);
                    if (dockData != null) {
                        dockData.forEach(d -> {
                            d.setDock(true);
                        });
                    }
                    layoutData.setDockData(dockData);
                    layoutData.setDockKey(name);
                }
            }
        }

        return layoutData;
    }

    /**
     * 重启指定包名的应用
     */
    public static void restartApp(String packageName) throws Exception {
        if (!hasRootAccess()) {
            throw new Exception("Root access required for restarting apps");
        }

        String killCommand = String.format("kill -9 `pidof %s`", packageName);
        executeRootCommand(killCommand, "Failed to restart app: " + packageName);
        Log.i(TAG, "App restarted: " + packageName);
    }

    /**
     * 重置主屏桌面布局 - 清除所有XML文件中的applist.data键
     */
    public static void resetMainLauncherLayout() throws Exception {
        String mainLauncherPath = "/data/user_de/0/com.hozonauto.netahome/shared_prefs/";
        resetLayoutData(mainLauncherPath, "applist.data", "com.hozonauto.netahome");
    }

    /**
     * 重置副屏桌面布局 - 清除所有XML文件中匹配VICE_LAUNCHER_PATTERN的键
     */
    public static void resetViceLauncherLayout() throws Exception {
        String viceLauncherPath = "/data/user_de/10/com.android.vicelauncher/shared_prefs/";
        resetLayoutData(viceLauncherPath, null, "com.android.vicelauncher");
    }

    /**
     * 重置布局数据的通用方法
     */
    private static void resetLayoutData(String dirPath, String specificKey, String packageName) throws Exception {
        if (!hasRootAccess()) {
            throw new Exception("Root access required for resetting layout data");
        }

        // 获取目录下所有XML文件
        String listCommand = String.format("find '%s' -name '*.xml' -type f", dirPath);
        String xmlFiles = executeRootCommandWithOutput(listCommand);

        if (xmlFiles.trim().isEmpty()) {
            Log.i(TAG, "No XML files found in directory: " + dirPath);
            return;
        }

        String[] fileList = xmlFiles.trim().split("\n");

        for (String xmlFilePath : fileList) {
            xmlFilePath = xmlFilePath.trim();
            if (xmlFilePath.isEmpty()) continue;

            try {
                File xmlFile = new File(xmlFilePath);
                resetXmlFileLayoutData(xmlFile, specificKey);
                Log.i(TAG, "Reset layout data in file: " + xmlFilePath);
            } catch (Exception e) {
                Log.e(TAG, "Failed to reset layout data in file: " + xmlFilePath, e);
            }
        }

        // 重启对应的应用
        restartApp(packageName);
    }

    /**
     * 重置单个XML文件中的布局数据
     */
    private static void resetXmlFileLayoutData(File xmlFile, String specificKey) throws Exception {
        // Generate timestamp for file naming
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmss", Locale.getDefault());
        String timestamp = dateFormat.format(new Date());

        // Create file names with timestamp
        String originalName = xmlFile.getName();
        String nameWithoutExt = originalName.substring(0, originalName.lastIndexOf('.'));
        String extension = originalName.substring(originalName.lastIndexOf('.'));

        File parentDir = xmlFile.getParentFile();
        File tmpFile = File.createTempFile("netamade_reset", ".xml");
        File bakFile = new File(parentDir, nameWithoutExt + "_bak_" + timestamp + extension);

        // Read and modify the XML file
        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
        DocumentBuilder builder = factory.newDocumentBuilder();
        Document document = builder.parse(xmlFile);

        NodeList stringNodes = document.getElementsByTagName("string");
        boolean hasChanges = false;

        // Remove matching string elements
        for (int i = stringNodes.getLength() - 1; i >= 0; i--) {
            Node node = stringNodes.item(i);
            if (node.getNodeType() == Node.ELEMENT_NODE) {
                Element element = (Element) node;
                String name = element.getAttribute("name");

                boolean shouldRemove = false;
                if (specificKey != null) {
                    // For main launcher, remove specific key
                    shouldRemove = specificKey.equals(name);
                } else {
                    // For vice launcher, remove keys matching pattern
                    shouldRemove = VICE_LAUNCHER_APP_PATTERN.matcher(name).matches();
                }

                if (!shouldRemove) {
                    shouldRemove = VICE_LAUNCHER_DOCK_PATTERN.matcher(name).matches();
                }

                if (shouldRemove) {
                    element.getParentNode().removeChild(element);
                    hasChanges = true;
                    Log.i(TAG, "Removed key: " + name);
                }
            }
        }

        if (!hasChanges) {
            Log.i(TAG, "No layout data keys found to remove in file: " + xmlFile.getName());
            tmpFile.delete();
            return;
        }

        // Save modified content to temp file
        TransformerFactory transformerFactory = TransformerFactory.newInstance();
        Transformer transformer = transformerFactory.newTransformer();
        DOMSource source = new DOMSource(document);
        StreamResult result = new StreamResult(tmpFile);
        transformer.transform(source, result);

        // Backup and replace with root permissions
        backupAndReplaceWithRoot(xmlFile, tmpFile, bakFile);
        tmpFile.delete();
    }

    public static void saveXmlFile(File originalFile, LayoutData layoutData) throws Exception {
        File tmpFile = File.createTempFile("netamade", ".xml");
        executeRootCommandWithOutput(String.format("cp '%s' '%s' && chmod 0777 '%s'", originalFile.getAbsolutePath(), tmpFile.getAbsolutePath(), tmpFile.getAbsolutePath()));
        // Generate timestamp for file naming
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmss", Locale.getDefault());
        String timestamp = dateFormat.format(new Date());

        // Create file names with timestamp
        String originalName = originalFile.getName();
        String nameWithoutExt = originalName.substring(0, originalName.lastIndexOf('.'));
        String extension = originalName.substring(originalName.lastIndexOf('.'));

        File parentDir = originalFile.getParentFile();
        File modFile = new File(parentDir, nameWithoutExt + "_mod_" + timestamp + extension);
        File bakFile = new File(parentDir, nameWithoutExt + "_bak_" + timestamp + extension);

        // Read and modify the original file
        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
        DocumentBuilder builder = factory.newDocumentBuilder();
        Document document = builder.parse(tmpFile);

        NodeList stringNodes = document.getElementsByTagName("string");

        for (int i = 0; i < stringNodes.getLength(); i++) {
            Node node = stringNodes.item(i);
            if (node.getNodeType() == Node.ELEMENT_NODE) {
                Element element = (Element) node;
                String name = element.getAttribute("name");

                if ("applist.data".equals(name) && layoutData.getAppListData() != null) {
                    element.setTextContent(gson.toJson(layoutData.getAppListData()));
                } else if ("dock.data".equals(name) && layoutData.getAppListData() != null) {
                    element.setTextContent(gson.toJson(layoutData.getDockData()));
                } else if (layoutData.getAppListKey() != null && layoutData.getAppListKey().equals(name)) {
                    element.setTextContent(gson.toJson(layoutData.getAppListData()));
                } else if (layoutData.getDockKey() != null && layoutData.getDockKey().equals(name)) {
                    element.setTextContent(gson.toJson(layoutData.getDockData()));
                }
            }
        }

        // Save modified content to _mod_ file
        TransformerFactory transformerFactory = TransformerFactory.newInstance();
        Transformer transformer = transformerFactory.newTransformer();
        DOMSource source = new DOMSource(document);
        StreamResult result = new StreamResult(tmpFile);
        transformer.transform(source, result);

        // Copy modified file to _mod_ file with root
        String copyCommand = String.format("cp '%s' '%s'", tmpFile.getAbsolutePath(), modFile.getAbsolutePath());
        executeRootCommand(copyCommand, "Failed to copy modified file");
        tmpFile.delete();

        Log.i(TAG, "Modified file saved to: " + modFile.getAbsolutePath());

        // Backup original file and replace with modified content using root
        backupAndReplaceWithRoot(originalFile, modFile, bakFile);
    }

    private static List<AppIconItem> parseAppIconList(String jsonContent) {
        try {
            return gson.fromJson(jsonContent, APP_ICON_LIST_TYPE);
        } catch (Exception e) {
            Log.e(TAG, "Failed to parse app icon list", e);
            return new ArrayList<>();
        }
    }

    private static File copyToTempFile(File originalFile) throws IOException {
        File tempFile = File.createTempFile("shared_prefs_", ".xml");

        try {
            executeRootCommand(String.format("cp '%s' '%s' && chmod 0777 '%s'", originalFile.getAbsoluteFile(), tempFile.getAbsolutePath(), tempFile.getAbsolutePath()), "复制到临时文件");
        } catch (Exception e) {
            Log.i("Root", "Failed to copy file", e);
            return originalFile;
        }

        return tempFile;
    }

    /**
     * 备份原文件并使用 root 权限替换源文件，保持权限一致
     */
    private static void backupAndReplaceWithRoot(File originalFile, File modFile, File bakFile) throws Exception {
        String originalPath = originalFile.getAbsolutePath();
        String modPath = modFile.getAbsolutePath();
        String bakPath = bakFile.getAbsolutePath();

        Log.i(TAG, "Starting backup and replace process:");
        Log.i(TAG, "Original: " + originalPath);
        Log.i(TAG, "Modified: " + modPath);
        Log.i(TAG, "Backup: " + bakPath);

        // Check if we have root access
        if (!hasRootAccess()) {
            throw new Exception("Root access required for file replacement in system directories");
        }

        // Step 1: Backup original file using root
        String backupCommand = String.format("cp '%s' '%s'", originalPath, bakPath);
        executeRootCommand(backupCommand, "Failed to backup original file");
        Log.i(TAG, "Original file backed up to: " + bakPath);

        // Step 2: Get original file permissions and ownership
        String statCommand = String.format("stat -c '%%a %%U %%G' '%s'", originalPath);
        String statResult = executeRootCommandWithOutput(statCommand);
        String[] statParts = statResult.trim().split("\\s+");
        if (statParts.length != 3) {
            throw new Exception("Failed to get file permissions and ownership");
        }

        // Step 3: Replace original file with modified content
        String replaceCommand = String.format("cp '%s' '%s'", modPath, originalPath);
        executeRootCommand(replaceCommand, "Failed to replace original file");
        Log.i(TAG, "Original file replaced with modified content");

        // Step 4: Restore original permissions and ownership
        String chmodCommand = String.format("chmod 0777 '%s'", originalPath);
        executeRootCommand(chmodCommand, "Failed to restore file permissions");

        Log.i(TAG, "File permissions and ownership restored");
        Log.i(TAG, "File replacement completed successfully");
    }

    /**
     * 检查是否有 root 权限
     */
    public static boolean hasRootAccess() {
        try {
            Process process = Runtime.getRuntime().exec("su -c id");
            java.io.BufferedReader reader = new java.io.BufferedReader(
                    new java.io.InputStreamReader(process.getInputStream()));
            String result = reader.readLine();
            reader.close();

            int exitCode = process.waitFor();
            return exitCode == 0 && result != null && result.contains("uid=0");
        } catch (Exception e) {
            Log.e(TAG, "Failed to check root access", e);
            return false;
        }
    }

    /**
     * 执行 root 命令
     */
    private static void executeRootCommand(String command, String errorMessage) {
        Shell.Result result = Shell.cmd(command).exec();
        if (result.isSuccess()) {
            Log.e("Root", "Failed to execute command: " + errorMessage + " \n" + result.getErr());
        }
    }

    /**
     * 执行 root 命令并返回输出
     */
    private static String executeRootCommandWithOutput(String command) throws Exception {
        Shell.Result result = Shell.cmd(command).exec();
        if (result.isSuccess()) {
            Log.e("Root", "Failed to execute command: " + result.getErr());
        }
        StringBuilder sb = new StringBuilder();

        for (String s : result.getOut()) {
            sb.append(s).append("\n");
        }
        for (String s : result.getErr()) {
            sb.append(s).append("\n");
        }

        return sb.toString();
    }
}
