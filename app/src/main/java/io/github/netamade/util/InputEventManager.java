package io.github.netamade.util;

import android.hardware.input.InputManager;
import android.os.SystemClock;
import android.view.InputEvent;
import android.view.KeyEvent;

import io.github.netamade.AppContext;

public class InputEventManager {

    public static void injectKeyEvent(int code, int displayId, int actionDownTime) {

        actionDownTime += 10;

        final long eventTime = SystemClock.uptimeMillis();
        InputManager im = AppContext.applicationContext().getSystemService(InputManager.class);


        // downEvent的开始时间和结束时间相差actionDownTime毫秒
        KeyEvent downEvent = new KeyEvent(eventTime, eventTime + actionDownTime, KeyEvent.ACTION_DOWN, code, 0);
        // upEvent紧接着downEvent的结束时间发生
        KeyEvent upEvent = new KeyEvent(eventTime + actionDownTime, eventTime + actionDownTime + 25, KeyEvent.ACTION_UP, code, 0);

        ReflectUtils.setField(downEvent, "mDisplayId", displayId);
        ReflectUtils.setField(upEvent, "mDisplayId", displayId);

        ReflectUtils.invoke(im, "injectInputEvent", new Class[]{InputEvent.class, int.class}, downEvent, 0);
        ReflectUtils.invoke(im, "injectInputEvent", new Class[]{InputEvent.class, int.class}, upEvent, 0);
    }

}
