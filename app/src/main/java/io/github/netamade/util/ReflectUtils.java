package io.github.netamade.util;


import android.util.Log;

import java.lang.reflect.Field;
import java.lang.reflect.Method;

public class ReflectUtils {

    /**
     * 无异常抛出的反射调用
     *
     * @param target     目标对象
     * @param methodName 方法名
     * @return 方法返回值 Object
     */
    public static Object invoke(Object target, String methodName) {
        return invoke(target, methodName, null);
    }

    /**
     * 无异常抛出的反射调用
     *
     * @param target         目标对象
     * @param methodName     方法名
     * @param parameterTypes 参数类型
     * @param parameters     参数列表
     * @return 方法返回值 Object
     */
    public static Object invoke(Object target, String methodName, Class<?>[] parameterTypes, Object... parameters) {
        if (target == null) {
            return null;
        }
        try {
            Method method = findTargetMethod(target.getClass(), methodName, parameterTypes);
            method.setAccessible(true);
            return method.invoke(target, parameters);
        } catch (Throwable e) {
            Log.e("Reflect", "invoke", e);
        }
        return null;
    }

    public static Object invokeStatic(Class<?> clazz, String methodName) {
        return invokeStatic(clazz, methodName, null);
    }

    /**
     * 静态方法反射调用
     *
     * @param methodName     方法名
     * @param parameterTypes 参数类型
     * @param parameters     参数列表
     * @return 方法返回值 Object
     */
    public static Object invokeStatic(Class<?> clazz, String methodName, Class<?>[] parameterTypes,
                                      Object... parameters) {
        if (clazz == null) {
            return null;
        }
        try {
            Method method = findTargetMethod(clazz, methodName, parameterTypes);
            method.setAccessible(true);
            return method.invoke(null, parameters);
        } catch (Throwable ignored) {
        }
        return null;
    }

    /**
     * 向父类此项寻找目的方法
     */
    private static Method findTargetMethod(Class<?> clazz, String methodName, Class<?>[] parameterTypes) {
        if (clazz == null) {
            return null;
        }
        try {
            return clazz.getMethod(methodName, parameterTypes);
        } catch (Throwable ignored) {
            try {
                return clazz.getDeclaredMethod(methodName, parameterTypes);
            } catch (Throwable ignore) {
            }
        }
        if (clazz == Object.class) {
            return null;
        }
        return findTargetMethod(clazz.getSuperclass(), methodName, parameterTypes);
    }

    /**
     * 反射调用并返回 泛型
     *
     * @param target         目标对象
     * @param methodName     方法名
     * @param parameterTypes 参数类型
     * @param parameters     参数列表
     * @return 方法返回值 泛型
     */
    public static <T> T invokeAsType(Object target, String methodName, Class<?>[] parameterTypes, Object[] parameters, Class<T> classType) {
        Object ret = invoke(target, methodName, parameterTypes, parameters);
        return ret == null ? null : classType.cast(ret);
    }

    public static String invokeAsString(Object target, String methodName) {
        return invokeAsString(target, methodName, null);
    }


    /**
     * 反射调用并返回 String
     *
     * @param target         目标对象
     * @param methodName     方法名
     * @param parameterTypes 参数类型
     * @param parameters     参数列表
     * @return 方法返回值 String
     */
    public static String invokeAsString(Object target, String methodName, Class<?>[] parameterTypes,
                                        Object... parameters) {
        return invokeAsType(target, methodName, parameterTypes, parameters, String.class);
    }

    /**
     * 反射调用并调用其toString()
     *
     * @param target         目标对象
     * @param methodName     方法名
     * @param parameterTypes 参数类型
     * @param parameters     参数列表
     * @return 方法返回值 String
     */
    public static String invokeToString(Object target, String methodName, Class<?>[] parameterTypes,
                                        Object... parameters) {
        Object invoke = invoke(target, methodName, parameterTypes, parameters);
        return invoke == null ? null : invoke.toString();
    }

    public static String invokeToString(Object target, String methodName) {
        return invokeToString(target, methodName, null);
    }

    /**
     * 获取字段
     */
    public static Object getField(Object target, String fieldName) {
        if (target == null) {
            return null;
        }
        try {
            Field field = findTargetField(target.getClass(), fieldName);
            field.setAccessible(true);
            return field.get(target);
        } catch (Throwable ignored) {
        }
        return null;
    }

    /**
     * 向上寻找字段
     */
    private static Field findTargetField(Class<?> clazz, String fieldName) {
        if (clazz == null) {
            return null;
        }
        try {
            return clazz.getField(fieldName);
        } catch (Throwable ignored) {
            try {
                return clazz.getDeclaredField(fieldName);
            } catch (Throwable ignore) {
            }
        }
        if (clazz == Object.class) {
            return null;
        }
        return findTargetField(clazz.getSuperclass(), fieldName);
    }

    public static void setField(Object target, String field, Object value) {
        try {
            Field targetField = findTargetField(target.getClass(), field);
            targetField.setAccessible(true);
            targetField.set(target, value);
        } catch (Throwable ignored) {
        }

    }
}
