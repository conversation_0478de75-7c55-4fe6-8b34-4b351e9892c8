package io.github.netamade.util;

import java.io.PrintWriter;
import java.io.StringWriter;

public class ThrowableUtils {
    public static String toString(Throwable e) {
        return toString(e, true);
    }

    public static String toString(Throwable e, boolean line) {
        if (e == null) {
            return "";
        }
        if (!line) {
            return e.getMessage();
        }
        StringWriter out = new StringWriter();
        PrintWriter printWriter = new PrintWriter(out);
        e.printStackTrace(printWriter);
        return out.toString();
    }
}
