package io.github.netamade.util;

import android.content.Context;
import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.pm.ResolveInfo;
import android.graphics.Bitmap;
import android.graphics.Bitmap.Config;
import android.graphics.Canvas;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.util.Log;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import io.github.netamade.AppSettings;
import io.github.netamade.entity.AppInfo;
import io.github.netamade.entity.AppShortcut;

public class ApkUtils {

    public static Bitmap drawableToBitmap(Drawable drawable) {
        Bitmap createBitmap;
        Canvas canvas;
        if (drawable instanceof BitmapDrawable) {
            BitmapDrawable bitmapDrawable = (BitmapDrawable) drawable;
            if (bitmapDrawable.getBitmap() != null) {
                return bitmapDrawable.getBitmap();
            }
        }
        if (drawable.getIntrinsicWidth() > 0) {
            if (drawable.getIntrinsicHeight() > 0) {
                createBitmap = Bitmap.createBitmap(drawable.getIntrinsicWidth(), drawable.getIntrinsicHeight(), Config.ARGB_8888);
                canvas = new Canvas(createBitmap);
                drawable.setBounds(0, 0, canvas.getWidth(), canvas.getHeight());
                drawable.draw(canvas);
                return createBitmap;
            }
        }
        createBitmap = Bitmap.createBitmap(1, 1, Config.ARGB_8888);
        canvas = new Canvas(createBitmap);
        drawable.setBounds(0, 0, canvas.getWidth(), canvas.getHeight());
        drawable.draw(canvas);
        return createBitmap;
    }


    public static AppInfo parseApk(PackageManager packageManager, PackageInfo packageInfo) {
        return parseApk(packageManager, packageInfo, true);
    }

    public static AppInfo parseApk(PackageManager packageManager, PackageInfo packageInfo, boolean loadAppIcon) {
        AppInfo appInfo = new AppInfo();
        appInfo.setAppName(packageInfo.applicationInfo.loadLabel(packageManager).toString());
        appInfo.setPackageName(packageInfo.packageName);
        if (loadAppIcon) {
            appInfo.setIcon(loadAppIcon(packageManager, packageInfo, null));
        }
        appInfo.setActivities(packageInfo.activities);
        appInfo.setVersionName(packageInfo.versionName);
        appInfo.setVersionCode(packageInfo.getLongVersionCode());
        appInfo.setApkPlacement(packageInfo.applicationInfo.sourceDir);
        appInfo.setSystem((packageInfo.applicationInfo.flags & ApplicationInfo.FLAG_SYSTEM) != 0);
        appInfo.setFrozen(packageInfo.applicationInfo.enabled);
        appInfo.setLastUpdateTime(packageInfo.lastUpdateTime);
        appInfo.setFirstInstallTime(packageInfo.firstInstallTime);

        AppShortcut defaultShortCut = ApkUtils.getDefaultLaunchActivity(packageManager, appInfo.getPackageName());

        Set<String> anyTouchAppShortCuts = AppSettings.getAnyTouchAppShortCuts();


        if (packageInfo.activities != null) {
            List<AppShortcut> shortcuts = new ArrayList<>();

            for (ActivityInfo activity : packageInfo.activities) {

                if (!activity.exported || !activity.enabled) {
                    continue;
                }

                AppShortcut shortcut = new AppShortcut(activity.loadLabel(packageManager).toString(), activity.packageName, activity.name);

                if (anyTouchAppShortCuts.contains(shortcut.toString())) {

                    appInfo.setShortcutAdded(true);

                }

                if (defaultShortCut != null && Objects.equals(activity.name, defaultShortCut.getActivityClass())) {
                    shortcut.setDefault(true);
                    shortcuts.add(0, shortcut);
                } else {
                    shortcuts.add(shortcut);
                }
            }

            appInfo.setShortcuts(shortcuts);
        }

        return appInfo;
    }

    public static AppShortcut getDefaultLaunchActivity(PackageManager packageManager, String packageName) {

        try {
            Intent intent = new Intent(Intent.ACTION_MAIN);
            intent.addCategory(Intent.CATEGORY_LAUNCHER);
            intent.setPackage(packageName);

            ResolveInfo resolveInfo = packageManager.resolveActivity(intent, 0);
            if (resolveInfo != null) {
                return new AppShortcut(resolveInfo.loadLabel(packageManager).toString(), resolveInfo.resolvePackageName, resolveInfo.activityInfo.name);
            }
        } catch (Throwable ignore) {
        }

        return null;
    }

    public static List<AppShortcut> getLaunchActivities(PackageManager packageManager, String packageName) {

        List<AppShortcut> shortcuts = new ArrayList<>();

        try {

            PackageInfo packageInfo = packageManager.getPackageInfo(packageName, PackageManager.GET_ACTIVITIES);
            ActivityInfo[] activities = packageInfo.activities;

            for (ActivityInfo activityInfo : activities) {
                Intent intent = new Intent(Intent.ACTION_MAIN);
                intent.addCategory(Intent.CATEGORY_LAUNCHER);
                intent.setClassName(packageName, activityInfo.name);

                if (packageManager.resolveActivity(intent, 0) != null) {
                    shortcuts.add(new AppShortcut(activityInfo.loadLabel(packageManager).toString(), packageName, activityInfo.name));
                }
            }
        } catch (PackageManager.NameNotFoundException ignore) {
        }

        return shortcuts;
    }

    public static Drawable loadAppIcon(PackageManager packageManager, PackageInfo packageInfo, String activityClass) {
        if (activityClass != null && packageInfo.activities != null) {

            for (ActivityInfo activity : packageInfo.activities) {
                if (activity.name.equals(activityClass)) {
                    return activity.loadIcon(packageManager);
                }
            }

        }
        return packageInfo.applicationInfo.loadIcon(packageManager);
    }

    public static ActivityInfo[] getApkActivities(Context context, AppInfo appInfo) {
        PackageInfo archiveInfo = context.getPackageManager().getPackageArchiveInfo(appInfo.getApkPlacement(), 0);
        if (archiveInfo == null) {
            return null;
        }
        return archiveInfo.activities;
    }
}
