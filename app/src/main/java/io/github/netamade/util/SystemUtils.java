package io.github.netamade.util;

import android.content.Context;
import android.view.WindowManager;
import android.view.WindowMetrics;

import io.github.netamade.AppContext;

public class SystemUtils {
    public static int getDisplayHeight() {
        final WindowMetrics metrics = ((WindowManager) AppContext.applicationContext().getSystemService(Context.WINDOW_SERVICE)).getCurrentWindowMetrics();
        return metrics.getBounds().height();
    }

    public static int getDisplayWidth() {
        final WindowMetrics metrics = ((WindowManager) AppContext.applicationContext().getSystemService(Context.WINDOW_SERVICE)).getCurrentWindowMetrics();
        return metrics.getBounds().width();
    }

    public static int dpToPx(int dp) {
        float density = AppContext.applicationContext().getResources().getDisplayMetrics().density;
        return Math.round(dp * density);
    }
}
