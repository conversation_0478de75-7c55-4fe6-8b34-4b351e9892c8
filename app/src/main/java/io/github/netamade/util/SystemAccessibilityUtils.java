package io.github.netamade.util;

import android.content.ContentResolver;
import android.provider.Settings;
import android.util.Log;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.locks.ReentrantLock;

import io.github.netamade.AppContext;
import io.github.netamade.AppSettings;

public class SystemAccessibilityUtils {

    public static final String TAG = SystemAccessibilityUtils.class.getSimpleName();

    private static final ReentrantLock accessibilityChangeLock = new ReentrantLock();

    public static void keepAccessibilityServiceAlive() {
        if (accessibilityChangeLock.isLocked()) {
            return;
        }

        accessibilityChangeLock.lock();
        try {

            Set<String> keepEnabledAccessibilityServiceIds = AppSettings.getKeepEnabledAccessibilityServiceIds();
            Set<String> systemEnabledAccessibilityServices = SystemAccessibilityUtils.getEnabledServiceIds();
            Set<String> result = new HashSet<>(systemEnabledAccessibilityServices);

            for (String serviceId : keepEnabledAccessibilityServiceIds) {

                if (result.add(serviceId)) {

                    Log.i(TAG, "keep accessibility service enabled for serviceId: " + serviceId);

                }

            }

            setEnabledServiceIds(result);

        } finally {
            accessibilityChangeLock.unlock();
        }
    }

    public static String getSystemEnabledAccessibilityServices() {
        ContentResolver contentResolver = AppContext.applicationContext().getContentResolver();
        try {

            if (Settings.Secure.getString(contentResolver, Settings.Secure.ACCESSIBILITY_ENABLED) == null) {
                try {

                    Settings.Secure.putString(contentResolver, Settings.Secure.ACCESSIBILITY_ENABLED, "1");

                } catch (Exception ignored) {

                }
            }

            String s = Settings.Secure.getString(contentResolver, Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES);
            return s == null ? "" : s;
        } catch (Throwable e) {
            Log.e(TAG, "获取无障碍服务数据失败: ", e);
            return "";
        }
    }

    public static Set<String> getEnabledServiceIds() {

        String enabledAccessibilityServices = getSystemEnabledAccessibilityServices();
        String[] serviceIds = enabledAccessibilityServices.split(":");

        return new HashSet<>(Arrays.asList(serviceIds));
    }

    public static void setEnabledServiceIds(Set<String> enabledServiceIds) {

        StringBuilder sb = new StringBuilder();
        for (String serviceId : enabledServiceIds) {
            sb.append(serviceId).append(":");
        }
        if (sb.length() > 0) {
            sb.deleteCharAt(sb.length() - 1);
        }
        String serviceIds = sb.toString();
        ContentResolver contentResolver = AppContext.applicationContext().getContentResolver();

        Log.i(TAG, "========= setEnabledServiceIds => " + ReflectUtils.invoke(contentResolver, "getUserId"));

        try {
            Log.i(TAG, "setEnabledServiceIds: " + serviceIds);
            Settings.Secure.putString(contentResolver, Settings.Secure.ENABLED_ACCESSIBILITY_SERVICES, serviceIds);
        } catch (Throwable e) {
            Log.e(TAG, "failed to setEnabledServiceIds: " + serviceIds, e);
        }

    }
}
