package io.github.netamade.util.collection;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class IndexableMap<K, V> {
    private final List<K> indexes = new ArrayList<>();
    private final Map<K, V> data = new HashMap<>();

    public int put(K k, V v) {
        if (data.put(k, v) == null) {
            indexes.add(k);
            return indexes.size() - 1;
        }
        return indexes.indexOf(k);
    }

    public int size() {
        return indexes.size();
    }

    public int keyAt(K k) {
        return indexes.indexOf(k);
    }

    public K indexOf(int index) {
        return indexes.get(index);
    }

    public V valueAt(int index) {
        K key = indexOf(index);
        return data.get(key);
    }

}
