package io.github.netamade.util;

/**
 * 通知管理
 */
public class NotificationUtils {

    interface HozonNotificationProxy {
        boolean available();

        String setNotificationsEnabledForPackage(String pkg, int uid, boolean enabled);

        String areNotificationsEnabledForPackage(String pkg, int uid);

        String areNotificationsEnabled(String pkg);

        String cancelAllNotifications(String pkg, int userId);
    }

    private static HozonNotificationProxy hozonNotificationProxy = new HozonNotificationProxy() {
        @Override
        public boolean available() {
            return false;
        }

        @Override
        public String setNotificationsEnabledForPackage(String pkg, int uid, boolean enabled) {
            return "";
        }

        @Override
        public String areNotificationsEnabledForPackage(String pkg, int uid) {
            return "";
        }

        @Override
        public String areNotificationsEnabled(String pkg) {
            return "";
        }

        @Override
        public String cancelAllNotifications(String pkg, int userId) {
            return "";
        }
    };

}
