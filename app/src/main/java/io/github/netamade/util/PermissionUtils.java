package io.github.netamade.util;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.HozonPermissionManager;
import android.content.pm.PackageManager;
import android.util.Log;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

import io.github.netamade.AppContext;

public class PermissionUtils {

    public static final String TAG = PermissionUtils.class.getSimpleName();

    interface HozonPermissionProxy {

        boolean available();

        String getInfo();

        String grant(String permission);

        String revoke(String permission);

        int check(String permission);
    }

    public static String getInfo() {
        return hozonPermissionProxy.getInfo();
    }

    public static boolean hasRuntimePermission(String permission) {
        try {
            int check = hozonPermissionProxy.check(permission);
            return check == 0;
        } catch (Throwable e) {
            Log.i(TAG, "hasRuntimePermission failed", e);
            return false;
        }
    }

    public static String checkRuntimePermission(String permission) {
        try {
            int check = hozonPermissionProxy.check(permission);
            return check == 0 ? "已授权" : "未授权";
        } catch (Throwable e) {
            e.printStackTrace();
            return "检测失败";
        }
    }

    public static String grantRuntimePermission(String permission) {
        return hozonPermissionProxy.grant(permission);
    }

    public static String revokeRuntimePermission(String permission) {
        return hozonPermissionProxy.revoke(permission);
    }

    public static boolean hasWriteSecureSettingsPermission() {
        return AppContext.applicationContext().checkCallingOrSelfPermission(Manifest.permission.WRITE_SECURE_SETTINGS) == PackageManager.PERMISSION_GRANTED;
    }

    public static boolean hasSystemAlertWindowPermission() {
        return AppContext.applicationContext().checkCallingOrSelfPermission(Manifest.permission.SYSTEM_ALERT_WINDOW) == PackageManager.PERMISSION_GRANTED;
    }

    public static String getAdbCommandForWriteSecureSettings() {
        return "adb shell pm grant " + AppContext.applicationContext().getPackageName() + " " + Manifest.permission.WRITE_SECURE_SETTINGS;
    }


    private static HozonPermissionProxy hozonPermissionProxy = new HozonPermissionProxy() {

        public final Map<String, Integer> status = new HashMap<>();

        @Override
        public boolean available() {
            return false;
        }

        @Override
        public String getInfo() {
            return "";
        }

        @Override
        public int check(String permission) {
            return status.getOrDefault(permission, 0);
        }

        @Override
        public String grant(String permission) {
            Log.i(TAG, "emulate grant => " + permission);
            status.put(permission, 0);
            return null;
        }

        @Override
        public String revoke(String permission) {
            Log.i(TAG, "emulate revoke => " + permission);
            status.put(permission, -1);
            return null;
        }
    };

    static {
        try {

            // 判断是否是在车机系统中
            Class.forName("android.app.HozonPermissionManager");

            hozonPermissionProxy = new HozonPermissionProxy() {
                private static final int MAX_RETRIES = 5;

                @SuppressLint("WrongConstant")
                private HozonPermissionManager permissionManager() {
                    return (HozonPermissionManager) AppContext.applicationContext().getApplicationContext().getSystemService("hozon_permission");
                }

                @Override
                public boolean available() {
                    return getInfo().contains("revokeRuntimePermission");
                }

                @Override
                public String getInfo() {
                    HozonPermissionManager pm = permissionManager();
                    StringBuilder sb = new StringBuilder();
                    sb.append("className: ").append(pm.getClass().getName()).append('\n');

                    Method[] methods = pm.getClass().getDeclaredMethods();
                    for (Method m : methods) {
                        sb.append("getDeclaredMethods()/").append(m.getName()).append('\n');
                    }
                    Log.i(TAG, sb.toString());
                    return sb.toString();
                }

                @Override
                public String grant(String permission) {
                    return wrapExceptionRun(() -> permissionManager().grantRuntimePermission(permission));
                }

                @Override
                public String revoke(String permission) {
                    return wrapExceptionRun(() -> permissionManager().revokeRuntimePermission(permission));
                }

                @Override
                public int check(String permission) {
                    return permissionManager().checkPermission(permission);
                }

                private String wrapExceptionRun(Runnable runnable) {

                    try {

                        runnable.run();
                        return null;

                    } catch (Throwable e) {
                        e.printStackTrace();
                        Log.e(TAG, "修改授权失败", e);
                        return "修改授权失败: " + ThrowableUtils.toString(e, false);
                    }

                }

            };

        } catch (Throwable e) {
            Log.e(TAG, "failed to get HozonPermissionManager");
        }
    }

}
