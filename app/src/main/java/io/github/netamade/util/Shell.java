package io.github.netamade.util;

import android.util.Log;

import java.util.Arrays;

public class Shell {


    public static void root(String... args) {
        String[] cmdArr = new String[3];
        cmdArr[0] = "su";
        cmdArr[1] = "-c";

        StringBuilder sb = new StringBuilder();
        for (String arg : args) {
            if (arg.contains(" ")) {
                sb.append("'").append(arg).append("'").append(" ");
            } else {
                sb.append(arg).append(" ");
            }
        }
        cmdArr[2] = sb.toString();
        try {
            Process process = Runtime.getRuntime().exec(cmdArr);
            int exitCode = process.waitFor();
            Log.i("Shell", "exec: " + Arrays.toString(cmdArr) + " exit: " + exitCode);
        } catch (Exception e) {
            Log.e("Shell", "exec failed: " + Arrays.toString(cmdArr), e);
        }
    }

}
