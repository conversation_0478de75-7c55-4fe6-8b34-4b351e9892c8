package io.github.netamade;

import org.junit.Test;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Base64;

public class GenBase64Image {
    @Test
    public void gen() throws Exception {
        File file = new File("/home/<USER>/AndroidStudioProjects/Netamade/app/src/main/assets/back.png");
        String base64 = encodeFileToBase64(file);
        FileOutputStream os = new FileOutputStream(new File(file.getPath() + ".base64"));
        os.write(base64.getBytes());
        os.flush();
    }

    private static String encodeFileToBase64(File file) throws IOException {
        byte[] fileContent = new byte[(int) file.length()];
        try (FileInputStream fis = new FileInputStream(file)) {
            fis.read(fileContent);
        }
        return Base64.getEncoder().encodeToString(fileContent);
    }
}
