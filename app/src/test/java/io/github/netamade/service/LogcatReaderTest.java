package io.github.netamade.service;

import junit.framework.TestCase;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

import io.github.netamade.syslog.LogData;

public class LogcatReaderTest extends TestCase {


    public void test1() throws IOException {
        File file = new File("adb.log");
        BufferedReader reader = new BufferedReader(new FileReader(file, StandardCharsets.UTF_8));
        String s = null;
        int i = 0;

        while ((s = reader.readLine()) != null) {

            s = s.trim();
            if (s.isEmpty()) {
                continue;
            }
            LogData data = LogData.parse(s);
            if (data == null) {
                System.err.println("err: " + s);
            } else {
                System.out.println(data);
            }
            if (i++ == 1000) {
                return;
            }
        }
    }


}