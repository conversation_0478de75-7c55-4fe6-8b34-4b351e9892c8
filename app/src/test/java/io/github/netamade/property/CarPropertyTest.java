package io.github.netamade.property;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import junit.framework.TestCase;

import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CarPropertyTest extends TestCase {

    static class Prop {
        public String name;
        public String key;
        public int id;
    }

    public void testValues() throws Exception {
        Map<String, Prop> props = new HashMap<>();
        for (CarProperty it : CarProperty.values()) {
            Prop prop = new Prop();
            prop.name = it.name;
            prop.id = it.id;
            prop.key = it.key;

            props.put(prop.key, prop);
        }
        try (OutputStream os = Files.newOutputStream(Path.of("src/main/assets/car_properties.json"))) {
            os.write(new GsonBuilder().setPrettyPrinting().create().toJson(props).getBytes());
        }
    }
}