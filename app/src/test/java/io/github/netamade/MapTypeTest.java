//package io.github.netamade;
//
//import com.google.gson.GsonBuilder;
//
//import org.junit.Test;
//
//import java.io.FileWriter;
//import java.nio.charset.StandardCharsets;
//import java.util.HashMap;
//import java.util.LinkedHashMap;
//import java.util.Map;
//
//import io.github.netamade.entity.MapType;
//
//public class MapTypeTest {
//
//    @Test
//    public void toJson() throws Exception{
//        Map<String, String> keys = new HashMap<>(){
//            {
//                put("@百度地图", "@baidu_map");
//                put("@腾讯地图", "@tencent_map");
//                put("@高德地图", "@a_map");
//                put("@高德地图手机版", "@a_map_mobile");
//            }
//        };
//        Map<String, String> icons = new HashMap<>(){
//            {
//                put("@百度地图", "ic_baidu");
//                put("@腾讯地图", "ic_tencent");
//                put("@高德地图", "ic_amap");
//                put("@高德地图手机版", "ic_amap_mobile");
//            }
//        };
//        Map<String, Object> data = new LinkedHashMap<>();
//        for (MapType mt : MapType.values()) {
//            Map<String, Object> map = new LinkedHashMap<>();
//            String mapName = mt.getName();
//            String key = keys.get(mapName);
//            map.put("key", key);
//            map.put("name", mapName);
//            map.put("packageName", mt.getPackageName());
//            map.put("icon", icons.get(mapName));
//            map.put("launchActivity", mt.getActivityClass());
//            map.put("intent", mt.getPackageName() + "/" + mt.getActivityClass());
//            map.put("activities", mt.getActivities());
//            data.put(key, map);
//        }
//
//        String json = new GsonBuilder().setPrettyPrinting().create().toJson(data);
//
//        System.out.println(json);
//
//        FileWriter writer = new FileWriter("src/main/assets/shortcuts.json", StandardCharsets.UTF_8);
//        writer.write(json);
//        writer.close();
//    }
//}
