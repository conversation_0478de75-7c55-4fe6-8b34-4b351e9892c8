package io.github.netamade.exemptions;

import android.annotation.SuppressLint;
import android.os.Build;

import java.lang.reflect.Method;

public class Exemptions {

    public static final String TAG = Exemptions.class.getSimpleName();

    @SuppressLint("ObsoleteSdkInt")
    public static void hack() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P
                || (Build.VERSION.SDK_INT == Build.VERSION_CODES.O_MR1 && Build.VERSION.PREVIEW_SDK_INT > 0)) {
            System.loadLibrary("exemptions");
        }
    }

    @SuppressLint("BlockedPrivateApi")
    public static boolean test() {
        try {
            Class<?> runtimeClass = Class.forName("dalvik.system.VMRuntime");
            Method nativeLoadMethod = runtimeClass.getDeclaredMethod("setTargetSdkVersionNative", int.class);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
}