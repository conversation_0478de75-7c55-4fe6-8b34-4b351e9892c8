plugins {
    alias(libs.plugins.android.library)
}

android {
    namespace 'io.github.netamade.exemptions'
    compileSdk 30

    defaultConfig {
        minSdk 30

        ndk {
            abiFilters 'arm64-v8a', 'x86_64'
        }

        externalNativeBuild {
            cmake {
                cppFlags ""
            }
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    externalNativeBuild {
        cmake {
            path "src/main/cpp/CMakeLists.txt"
            version "3.22.1"
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {
}