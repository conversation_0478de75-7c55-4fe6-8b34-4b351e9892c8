[versions]
agp = "8.9.3"
libsu = "6.0.0"
customactivityoncrash = "2.4.0"
junit = "4.13.2"
junitVersion = "1.2.1"
espressoCore = "3.6.1"
kotlinBom = "1.8.22"
lombok = "1.18.36"
multitype = "4.3.0"
appcompat = "1.7.1"
preference = "1.2.1"
coordinatorlayout = "1.3.0"
material = "1.12.0"
activity = "1.10.1"
navigationFragment = "2.9.0"
navigationUi = "2.9.0"
gson = "2.11.0"
verticalStepperForm = "2.7.0"

[libraries]
libsu = { module = "com.github.topjohnwu.libsu:core", version.ref = "libsu" }
customactivityoncrash = { module = "cat.ereza:customactivityoncrash", version.ref = "customactivityoncrash" }
junit = { group = "junit", name = "junit", version.ref = "junit" }
ext-junit = { group = "androidx.test.ext", name = "junit", version.ref = "junitVersion" }
espresso-core = { group = "androidx.test.espresso", name = "espresso-core", version.ref = "espressoCore" }
kotlin-bom = { module = "org.jetbrains.kotlin:kotlin-bom", version.ref = "kotlinBom" }
lombok = { module = "org.projectlombok:lombok", version.ref = "lombok" }
multitype = { module = "com.drakeet.multitype:multitype", version.ref = "multitype" }
appcompat = { group = "androidx.appcompat", name = "appcompat", version.ref = "appcompat" }
preference = { group = "androidx.preference", name = "preference", version.ref = "preference" }
coordinatorlayout = { group = "androidx.coordinatorlayout", name = "coordinatorlayout", version.ref = "coordinatorlayout" }
material = { group = "com.google.android.material", name = "material", version.ref = "material" }
activity = { group = "androidx.activity", name = "activity", version.ref = "activity" }
navigation-fragment = { group = "androidx.navigation", name = "navigation-fragment", version.ref = "navigationFragment" }
navigation-ui = { group = "androidx.navigation", name = "navigation-ui", version.ref = "navigationUi" }
gson = { group = "com.google.code.gson", name = "gson", version.ref = "gson" }
vertical-stepper-form = { module = "com.ernestoyaquello.stepperform:vertical-stepper-form", version.ref = "verticalStepperForm" }

[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }
android-library = { id = "com.android.library", version.ref = "agp" }

