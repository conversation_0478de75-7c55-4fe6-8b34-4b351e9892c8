pluginManagement {
    repositories {
        google {
            content {
                includeGroupByRegex("com\\.android.*")
                includeGroupByRegex("com\\.google.*")
                includeGroupByRegex("androidx.*")
            }
        }
        mavenLocal()
        //阿里云maven镜像
        maven { url 'https://maven.aliyun.com/repository/public' }
        //阿里云maven镜像，google maven镜像
        maven { url 'https://maven.aliyun.com/repository/google' }
        //阿里云maven镜像，gradle plugin 镜像
        maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
        //腾讯云maven镜像
        maven { url 'https://mirrors.cloud.tencent.com/nexus/repository/maven-public/' }
        //华为云maven镜像
        maven { url 'https://mirrors.huaweicloud.com/repository/maven/' }
        //网易maven镜像
        maven {
            url 'http://mirrors.163.com/maven/repository/maven-public/'
            allowInsecureProtocol true
        }
        gradlePluginPortal()
    }
}
dependencyResolutionManagement {
    repositoriesMode.set(RepositoriesMode.FAIL_ON_PROJECT_REPOS)
    repositories {
        mavenLocal()
        maven { url "https://maven.aliyun.com/repository/google/" }
        maven { url "https://mirrors.huaweicloud.com/repository/maven/" }
        maven { url "https://jitpack.io" }
    }
}

rootProject.name = "Netamade"
include ':app'
include ':hidden-api-exemptions'
