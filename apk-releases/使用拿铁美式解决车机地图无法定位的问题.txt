使用【拿铁美式】解决车机地图长时间下电后丢失定位权限的问题

软件免费提供，倒卖狗死全家，看到这条消息还去倒卖的必死全家


1. 仔细阅读以下内容，确定你要使用的版本，不同的版本在后面步骤中涉及的内容不同


【三合一版本】
此版本最初供我开发调试用，里面可以启动百度地图、高德地图、腾讯地图车机版，推荐使用【腾讯地图启动器】

对应文件：哪吒美式_1.0_解决车机地图定位权限问题-三合一启动器.apk
包名：io.github.netamade
启动Intent(Activity): io.github.netamade/io.github.netamade.MainActivity
安装后的授权命令:
    adb shell pm grant io.github.netamade android.permission.WRITE_SECURE_SETTINGS
    adb shell pm grant io.github.netamade android.permission.SYSTEM_ALERT_WINDOW
白名单内容：
  {
    "appName": "哪吒美式",
    "className": "io.github.netamade.MainActivity",
    "cloudConfigFlag": true,
    "icon": "",
    "isNewAdd": false,
    "isUsed": false,
    "packageName": "io.github.netamade",
    "redPointFlag": false
  }


【腾讯地图启动器】
对应文件：哪吒美式_1.0_腾讯地图启动器_io.github.netamade.tencent.apk
包名：io.github.netamade.tencent
启动Intent(Activity): io.github.netamade.tencent/io.github.netamade.TencentMap
安装后的授权命令: adb shell pm grant io.github.netamade.tencent android.permission.WRITE_SECURE_SETTINGS

白名单内容：
  {
    "appName": "@腾讯地图",
    "className": "io.github.netamade.TencentMap",
    "cloudConfigFlag": true,
    "icon": "",
    "isNewAdd": false,
    "isUsed": false,
    "packageName": "io.github.netamade.tencent",
    "redPointFlag": false
  }


【百度地图启动器】
对应文件：哪吒美式_1.0_百度地图启动器_io.github.netamade.baidu.apk
包名：io.github.netamade.baidu
启动Intent(Activity): io.github.netamade.baidu/io.github.netamade.BaiduMap
安装后的授权命令: adb shell pm grant io.github.netamade.baidu android.permission.WRITE_SECURE_SETTINGS

白名单内容：
  {
    "appName": "@百度地图",
    "className": "io.github.netamade.BaiduMap",
    "cloudConfigFlag": true,
    "icon": "",
    "isNewAdd": false,
    "isUsed": false,
    "packageName": "io.github.netamade.baidu",
    "redPointFlag": false
  }

2. 正常安装对应的apk到车机后，执行安装后的授权命令，【三合一启动器】会有选择页面，其他的打开后会直接打开对应的软件，不会显示过多别的内容

3. 为了保证使用的体验，默认情况下，距离上次授权时间未超过4个小时，启动对应的应用时不会重新授权，因为授权需要执行时间，会拖慢你打开地图的时间。如果发现地图权限掉了需要重新授权，只需要在30秒内连续打开应用3次，会重新授权


软件免费提供，倒卖狗死全家，看到这条消息还去倒卖的必死全家

